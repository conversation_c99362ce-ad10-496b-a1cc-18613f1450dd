<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{MM-dd HH:mm:ss.SSS}[%5p][%t] %c{5} - %X{mdcData}%m%n</Pattern>
        </encoder>
    </appender>

    <logger name="org.springframework.jdbc.core.JdbcTemplate" level="INFO"/>
    <logger name="com.grpcserver.GrpcServerInterceptor" level="DEBUG"/>
    <root level="info">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>