package com.service.grpc;

import com.google.protobuf.util.JsonFormat;
import com.tools.JsonUtils;
import dao.bean.CheckUniqueCreateBean;
import dao.bean.CheckUniqueUpdateBean;
import dao.bean.CommentType;
import dao.repository.CheckUniqueDao;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import payment.grpc.commonproto.BooleanReply;
import payment.grpc.commonproto.IntegerReply;
import payment.grpc.proto.CheckUniqueProtoServiceGrpc;
import payment.grpc.proto.ProtoCreateCheckUniqueRequest;
import payment.grpc.proto.ProtoUpdateCheckUniqueRequest;

@GrpcService
@Slf4j
public class CheckUniqueProto extends CheckUniqueProtoServiceGrpc.CheckUniqueProtoServiceImplBase implements BaseProto {

    @Autowired
    CheckUniqueDao checkUniqueDao;

    @Override
    public void getCheckUniqueId(ProtoCreateCheckUniqueRequest request, StreamObserver<IntegerReply> responseObserver) {
        IntegerReply.Builder builder = IntegerReply.newBuilder();
        builder.setResultStatus(getProtoResultSuccess());
        try{
            String requestJson = JsonFormat.printer().print(request);
            CheckUniqueCreateBean bean = JsonUtils.fromJson(requestJson, CheckUniqueCreateBean.class);

            int result = checkUniqueDao.getCheckUniqueId(bean);
            builder.setData(result);
        }catch(DataAccessException e){
            log.error("DB fail.", e);
            builder.setResultStatus(getProtoResultDBError().setMessage(dbErrorText+e.getMessage()).build());
        }catch(Exception e){
            log.error("General fail.", e);
            builder.setResultStatus(getProtoResultFail().setMessage(generalErrorText+e.getMessage()).build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void insertCheckUnique(ProtoCreateCheckUniqueRequest request, StreamObserver<IntegerReply> responseObserver) {
        IntegerReply.Builder builder = IntegerReply.newBuilder();
        builder.setResultStatus(getProtoResultSuccess());
        try{
            String requestJson = JsonFormat.printer().print(request);
            CheckUniqueCreateBean bean = JsonUtils.fromJson(requestJson, CheckUniqueCreateBean.class);

            int result = checkUniqueDao.insertCheckUnique(bean);
            builder.setData(result);
        }catch(DataAccessException e){
            log.error("DB fail.", e);
            builder.setResultStatus(getProtoResultDBError().setMessage(dbErrorText+e.getMessage()).build());
        }catch(Exception e){
            log.error("General fail.", e);
            builder.setResultStatus(getProtoResultFail().setMessage(generalErrorText+e.getMessage()).build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateCheckUniqueValue(ProtoUpdateCheckUniqueRequest request, StreamObserver<BooleanReply> responseObserver) {
        BooleanReply.Builder builder = BooleanReply.newBuilder();
        builder.setResultStatus(getProtoResultSuccess());
        try{
            String requestJson = JsonFormat.printer().print(request);
            CheckUniqueUpdateBean bean = JsonUtils.fromJson(requestJson, CheckUniqueUpdateBean.class);

            boolean result = checkUniqueDao.updateCheckUniqueValue(bean);
            builder.setData(result);
        }catch(DataAccessException e){
            log.error("DB fail.", e);
            builder.setResultStatus(getProtoResultDBError().setMessage(dbErrorText+e.getMessage()).build());
        }catch(Exception e){
            log.error("General fail.", e);
            builder.setResultStatus(getProtoResultFail().setMessage(generalErrorText+e.getMessage()).build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
