package com.service.grpc;


import payment.grpc.commonproto.ProtoResultStatus;

public interface BaseProto {
    String successText = "Success";
    String generalErrorText = "General exception:";
    String dbErrorText = "DB exception:";

    ProtoResultStatus protoResultSuccess = ProtoResultStatus.newBuilder().setStatus("000").setMessage(successText).build();
    ProtoResultStatus.Builder protoResultFail = ProtoResultStatus.newBuilder().setStatus("999").setMessage(generalErrorText);
    ProtoResultStatus.Builder protoResultDBError = ProtoResultStatus.newBuilder().setStatus("998").setMessage(dbErrorText);

    default String getSuccessText() {
        return successText;
    }

    default String getGeneralErrorText() {
        return generalErrorText;
    }

    default String getDbErrorText() {
        return dbErrorText;
    }

    default ProtoResultStatus getProtoResultSuccess() {
        return protoResultSuccess;
    }

    default ProtoResultStatus.Builder getProtoResultFail() {
        return ProtoResultStatus.newBuilder().setStatus("999").setMessage(generalErrorText);
    }

    default ProtoResultStatus.Builder getProtoResultDBError() {
        return ProtoResultStatus.newBuilder().setStatus("998").setMessage(dbErrorText);
    }
}
