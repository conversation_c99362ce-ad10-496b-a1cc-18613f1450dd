package com.grpcserver;

import com.constant.CommonConstant;
import com.google.protobuf.MessageOrBuilder;
import com.tools.GrpcUtils;
import com.tools.SequenceUtils;
import io.grpc.*;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.dao.DataAccessException;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;

@GrpcGlobalServerInterceptor
@Slf4j
public class GrpcServerInterceptor implements ServerInterceptor {

    public static final List<String> IGNORE_LOG_LIST = Arrays.asList(
            "payment.grpc.TransactionGrpcService/GetTransactionStatusByTransId"
            ,"payment.grpc.TransactionGrpcService/GetTransactionStatusSummary"
    );

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(ServerCall<ReqT, RespT> serverCall, Metadata metadata, ServerCallHandler<ReqT, RespT> serverCallHandler) {
        String fullMethodName = serverCall.getMethodDescriptor().getFullMethodName();
        Metadata.Key<String> mdcValue = Metadata.Key.of(CommonConstant.MDC_VALUE, Metadata.ASCII_STRING_MARSHALLER);
        String mdcValueStr = metadata.get(mdcValue);

        String mdcData = mdcValueStr;
        if(StringUtils.isBlank(mdcValueStr)){
            mdcData = String.format("[GRPC-%s]", SequenceUtils.getAtomicString(5, "%05d"));
        }
        MDC.put(CommonConstant.MDC_NAME, mdcData);

        final ServerCall.Listener<ReqT> original = serverCallHandler.startCall(serverCall, metadata);
        return new ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT>(original) {
            final long startTime = Instant.now().toEpochMilli();
            final boolean ignoreLogPath = IGNORE_LOG_LIST.contains(fullMethodName);
            @Override
            public void onMessage(final ReqT message) {
                if(ignoreLogPath){
                    log.debug("Req Message:{} for the path:{}", GrpcUtils.getGrpcJsonString((MessageOrBuilder) message), fullMethodName);
                }else{
                    log.info("Req Message:{} for the path:{}", GrpcUtils.getGrpcJsonString((MessageOrBuilder) message), fullMethodName);
                }
                super.onMessage(message);
            }

            @Override
            public void onHalfClose() {
                try{
                    super.onHalfClose();
                } catch (Exception ex) {
                    log.error("onHalfClose error:{}", ex.getMessage(), ex);
                    handleException(ex, serverCall, metadata);
                }

            }

            @Override
            public void onCancel() {
                long endTime = Instant.now().toEpochMilli();
                if(ignoreLogPath) {
                    log.debug("[onCancel]process time: {} ms for the path:{}", endTime - startTime, fullMethodName);
                }else{
                    log.info("[onCancel]process time: {} ms for the path:{}", endTime - startTime, fullMethodName);
                }
                super.onCancel();
            }

            @Override
            public void onComplete() {
                long endTime = Instant.now().toEpochMilli();
                if(ignoreLogPath) {
                    log.debug("[onComplete]process time: {} ms for the path:{}", endTime - startTime, fullMethodName);
                }else{
                    log.info("[onComplete]process time: {} ms for the path:{}", endTime - startTime, fullMethodName);
                }
                super.onComplete();
            }

            @Override
            public void onReady() {
                super.onReady();
            }

            private void handleException(Exception exception, ServerCall<ReqT, RespT> serverCall, Metadata metadata) {
                if (exception instanceof IllegalArgumentException) {
                    serverCall.close(Status.INVALID_ARGUMENT.withDescription(exception.getMessage()), metadata);
                } else if(exception instanceof DataAccessException) {
                    serverCall.close(Status.INTERNAL.withDescription("DB Error."),metadata);
                } else{
                    serverCall.close(Status.UNKNOWN, metadata);
                }
            }
        };
    }
}
