<included>

    <appender name="cloud-watch" class="io.github.dibog.AwsLogAppender">

        <awsConfig>
            <credentials>
                <accessKeyId>********************</accessKeyId>
                <secretAccessKey>VYJ6ump0gssk1nrdR2L1VezE96hwTaHWJ3CIIdaR</secretAccessKey>
            </credentials>

            <profileName></profileName>

            <region>ap-south-1</region>
        </awsConfig>

        <createLogGroup>true</createLogGroup>
        <queueLength>500</queueLength>
        <groupName>In-MscPaymentSchedule-PROD</groupName>
        <streamName>MscPaymentSchedule</streamName>
        <dateFormat>yyyyMMdd_HH</dateFormat>

        <layout>
            <pattern>%d{MM-dd HH:mm:ss.SSS}[%5p] [%t]%X{mdcData} %c{5} - %m%n</pattern>
        </layout>

    </appender>

    <root level="info">
        <appender-ref ref="cloud-watch"/>
    </root>
</included>