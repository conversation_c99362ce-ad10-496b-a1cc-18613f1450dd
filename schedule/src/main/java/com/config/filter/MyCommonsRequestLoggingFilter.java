package com.config.filter;

import org.springframework.web.filter.CommonsRequestLoggingFilter;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class MyCommonsRequestLoggingFilter extends CommonsRequestLoggingFilter {

    private final String START_TIME = "request_start_time";

    @Override
    protected void beforeRequest(HttpServletRequest request, String message) {
        long startTime = Instant.now().toEpochMilli();
        request.setAttribute(START_TIME, startTime);
        super.beforeRequest(request, message);
    }

    @Override
    protected void afterRequest(HttpServletRequest request, String message) {
        long startTime = (long) request.getAttribute(START_TIME);
        long endTime = Instant.now().toEpochMilli();
        String logText = String.format("[StartTime:%s][Process:%s ms]",
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault()).toLocalTime(),
                endTime-startTime);
        super.afterRequest(request, logText+message);
    }

}
