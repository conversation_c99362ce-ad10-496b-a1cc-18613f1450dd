package com.grpcclient.transaction;

import com.enums.ResponseStatus;
import com.exception.GrpcException;
import com.grpcclient.BasePaymentServiceGrpc;
import com.tools.GrpcUtils;
import dao.entity.AcctTransExchangeCurrency;
import dao.repository.AcctTransExchangeCurrencyDao;
import org.springframework.stereotype.Service;
import payment.grpc.commonproto.BooleanReply;
import payment.grpc.proto.*;

import javax.annotation.PostConstruct;

import static com.tools.GrpcUtils.beanToProtoBean;

@Service
public class AcctTransExchangeCurrencyServiceGrpc extends BasePaymentServiceGrpc implements AcctTransExchangeCurrencyDao {

    TransactionGrpcServiceGrpc.TransactionGrpcServiceBlockingStub stub;

    @PostConstruct
    public void init(){
        stub = TransactionGrpcServiceGrpc.newBlockingStub(getServerChannel());
    }

    @Override
    public boolean createAcctTransExchangeCurrency(AcctTransExchangeCurrency entity) {
        try{
            ProtoCreateAcctTransExchangeCurrencyRequest request = beanToProtoBean(entity, ProtoCreateAcctTransExchangeCurrencyRequest.class);
            BooleanReply reply = stub.createAcctTransExchangeCurrency(request);
            if(isSuccessResultStatus(reply.getResultStatus())){
                return reply.getData();
            }
            return false;
        } catch (GrpcException e){
            logger.error("GrpcException: reply:{}", e.getGrpcReply(), e);
            throw e;
        } catch (Exception e) {
            logger.error("call grpc fail URL:{}, ErrorMessage:{}", getServerChannel().authority(), e.getMessage());
            throw new GrpcException(ResponseStatus.GrpcReplyFail);
        }
    }

    @Override
    public AcctTransExchangeCurrency searchAcctTransExchangeCurrencyByTransId(String transId) {
        try{
            ProtoSearchAcctTransExchangeCurrencyByTransIdRequest request = ProtoSearchAcctTransExchangeCurrencyByTransIdRequest.newBuilder().setTransId(transId).build();
            AcctTransExchangeCurrencyReply reply = stub.searchAcctTransExchangeCurrencyByTransId(request);
            if (isSuccessResultStatus(reply.getResultStatus()) && reply.hasData()) {
                ProtoAcctTransExchangeCurrency data = reply.getData();
                return GrpcUtils.protoToJavaBean(data, AcctTransExchangeCurrency.class);
            }
        } catch (GrpcException e){
            logger.error("GrpcException: reply:{}", e.getGrpcReply(), e);
            throw e;
        } catch (Exception e) {
            logger.error("call grpc fail URL:{}, ErrorMessage:{}", getServerChannel().authority(), e.getMessage());
            throw new GrpcException(ResponseStatus.GrpcReplyFail);
        }
        return null;
    }
}
