package com.grpcclient.transaction;

import com.enums.ResponseStatus;
import com.exception.GrpcException;
import com.grpcclient.BasePaymentServiceGrpc;
import dao.entity.AcctTransMemberReference;
import dao.repository.AcctTransMemberReferenceDao;
import org.springframework.stereotype.Service;
import payment.grpc.commonproto.BooleanReply;
import payment.grpc.proto.ProtoCreateAcctTransMemberReferenceRequest;
import payment.grpc.proto.TransactionGrpcServiceGrpc;

import javax.annotation.PostConstruct;

import static com.tools.GrpcUtils.beanToProtoBean;

@Service
public class AcctTransMemberReferenceServiceGrpc extends BasePaymentServiceGrpc implements AcctTransMemberReferenceDao {
    TransactionGrpcServiceGrpc.TransactionGrpcServiceBlockingStub stub;

    @PostConstruct
    public void init(){
        stub = TransactionGrpcServiceGrpc.newBlockingStub(getServerChannel());
    }

    @Override
    public boolean createAcctTransMemberReference(AcctTransMemberReference entity) {
        try{
            ProtoCreateAcctTransMemberReferenceRequest request = beanToProtoBean(entity, ProtoCreateAcctTransMemberReferenceRequest.class);
            BooleanReply reply = stub.createAcctTransMemberReference(request);
            if(isSuccessResultStatus(reply.getResultStatus())){
                return reply.getData();
            }
            return false;
        } catch (GrpcException e){
            logger.error("GrpcException: reply:{}", e.getGrpcReply(), e);
            throw e;
        } catch (Exception e) {
            logger.error("call grpc fail URL:{}, ErrorMessage:{}", getServerChannel().authority(), e.getMessage());
            throw new GrpcException(ResponseStatus.GrpcReplyFail);
        }

    }
}
