package com.grpcclient.member;

public enum MemberCurrencyCode {
    CNY(0),
    USDT(1),
    ;
    int value;
    MemberCurrencyCode(int value) {
        this.value = value;
    }
    
    public static MemberCurrencyCode getEnumByCurrencyCode(String currencyCode){
        for (MemberCurrencyCode memberCurrencyCode : MemberCurrencyCode.values()) {
            if (memberCurrencyCode.name().equalsIgnoreCase(currencyCode)) {
                return memberCurrencyCode;
            }
        }
        return null;
    }
}
