package com.exception;

import com.enums.ResponseStatus;

/**
 * Payment general exception is only log error message not log exception stack
 */
public class PaymentGeneralException extends PaymentException{
    public PaymentGeneralException() {
        super();
    }

    public PaymentGeneralException(String errorMessage) {
        super(errorMessage);
    }

    public PaymentGeneralException(ResponseStatus responseStatus) {
        super(responseStatus);
    }

    public PaymentGeneralException(ResponseStatus responseStatus, Object... fields) {
        super(responseStatus, fields);
    }

    public PaymentGeneralException(String errorMessage, Throwable exception) {
        super(errorMessage, exception);
    }

    public PaymentGeneralException(String errorMessage, ResponseStatus responseStatus) {
        super(errorMessage, responseStatus);
    }
}
