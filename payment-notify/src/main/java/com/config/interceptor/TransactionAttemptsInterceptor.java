package com.config.interceptor;

import com.config.interceptor.bean.RequestDateTempArray;
import com.controller.bean.ResponseVo;
import com.enums.ResponseStatus;
import com.exception.PaymentException;
import com.tools.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

import static com.controller.constant.PaymentUrlConstant.APP_API_APPROVE_SNATCH;
import static com.controller.constant.PaymentUrlConstant.REJECT_TIMEOUT;

/**
 * this interceptor is prevent double request attempt at the same time
 */
@Component
public class TransactionAttemptsInterceptor extends HandlerInterceptorAdapter {
    private static final Logger logger = LoggerFactory.getLogger(TransactionAttemptsInterceptor.class);

    @Autowired
    RequestDateTempArray requestDateTempArray;

    //before the actual handler will be executed
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws IOException {
        logger.debug("getServletPath:{}", request.getServletPath());
        String servletPath = request.getServletPath();
        String transactionId = getTransactionId(request, servletPath);
        logger.debug("request transactionId:{}", transactionId);
        if(StringUtils.isNotBlank(transactionId)){
            try {
                requestDateTempArray.add(transactionId, new Date());
            }catch(PaymentException e){
                if(e.getResponseStatus() == ResponseStatus.TooManyAttempts){
                    logger.error("[{}]add requestDateTempArray got Too Many Attempts: {}", transactionId, e.getMessage());
                    ResponseVo<Object> result = new ResponseVo<>();
                    result.setStatus(ResponseStatus.TooManyAttempts.getCode());
                    result.setMessage(ResponseStatus.TooManyAttempts.getDescription());

                    response.setContentType("application/json; charset=UTF-8");
                    response.getWriter().write(JsonUtils.toJson(result));
                    return false;
                }else{
                    logger.error("[{}]add requestDateTempArray error: {}", transactionId, e.getMessage());
                }
            }catch(Exception e){
                logger.error("[{}]add requestDateTempArray got exception: {}", transactionId, e.getMessage());
            }
        }
        return true;
    }

    private String getTransactionId(HttpServletRequest request, String servletPath) {
        String transactionId = null;
        try {
            if (REJECT_TIMEOUT.equalsIgnoreCase(servletPath)) {
                transactionId = request.getParameter("transactionId");
            } else if (StringUtils.startsWithIgnoreCase(APP_API_APPROVE_SNATCH, "/transaction/snatch/approve")) {
                transactionId = servletPath.substring(servletPath.lastIndexOf("/") + 1);
            }
        }catch(Exception e){
            logger.error("get transactionId error. path:{}", servletPath, e);
        }
        return transactionId;
    }

}
