package com.config.interceptor.bean;

import com.enums.ResponseStatus;
import com.exception.PaymentException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@Component
@Slf4j
public class RequestDateTempArray {
    private final String DATE_FORMAT = "HH:mm:ss.SSS";
    DateFormat formatter = new SimpleDateFormat(DATE_FORMAT);
    private int size = 100;
    private int limitSecond = 1;
    private Map<String, Date> map = Collections.synchronizedMap(new LinkedHashMap<String, Date>(){
        @Override
        protected boolean removeEldestEntry(Map.Entry eldest) {
            return size() > size;
        }
    });

    public void setSize(int size) {
        this.size = size;
    }

    public void setLimitSecond(int limitSecond) {
        this.limitSecond = limitSecond;
    }

    public Map<String, Date> getMap() {
        return map;
    }

    public void add(String key, Date value){
        Date preValue = map.put(key, value);
//        log.debug("original key:{}, preValue:{}, new value:{}", key, preValue == null? null: formatter.format(preValue), formatter.format(value));
        if(preValue != null){
            long limitTime = DateUtils.addSeconds(preValue, limitSecond).getTime();
            if (value.getTime() < limitTime){
                log.error("[==TooManyAttempts==][{}]new Date:{} less than limit Date:{}, {} ms, map:{}",
                        key, formatter.format(value), formatter.format(new Date(limitTime)), value.getTime()-limitTime, map.keySet());
                // put back original value
                map.put(key, preValue);
                throw new PaymentException(ResponseStatus.TooManyAttempts, key);
            }
        }
        log.debug("key:{}, size:{}, date:{}, map:{}", key, map.size(), formatter.format(map.get(key)), map.keySet());
    }
}
