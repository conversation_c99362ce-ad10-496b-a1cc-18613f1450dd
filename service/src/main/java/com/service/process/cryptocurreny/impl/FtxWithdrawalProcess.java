package com.service.process.cryptocurreny.impl;

import com.enums.ResponseStatus;
import com.exception.PaymentException;
import com.service.FtxService;
import com.service.bean.CryptocurrencyExchangeWithdrawalResponse;
import com.service.bean.FtxWithdrawalRequest;
import com.service.bean.FtxWithdrawalResponse;
import com.service.enums.BitfinexDepositMethod;
import dao.bean.MerchantCryptocurrencyAccount;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

@Slf4j
@Service
public class FtxWithdrawalProcess extends AbstractCryptocurrencyExchangeWithdrawalProcess{

    @Autowired
    FtxService ftxService;

    @Override
    protected BigDecimal getCryptocurrencyCurrentBalance(MerchantCryptocurrencyAccount account) {
        String apiKeyWrite = account.getApiKeyWrite();
        String apiSecretWrite = account.getApiSecretWrite();
        return ftxService.getBalance(apiKeyWrite, apiSecretWrite);
    }

    @Override
    protected CryptocurrencyExchangeWithdrawalResponse executeWithdrawalProcess(MerchantCryptocurrencyAccount account, String address, BitfinexDepositMethod method, BigDecimal withdrawalAmount) {
        String apiKeyWrite = account.getApiKeyWrite();
        String apiSecretWrite = account.getApiSecretWrite();
        String withdrawalPassword = account.getPassword();

        FtxWithdrawalRequest requestBean = FtxWithdrawalRequest.builder()
                .address(address)
                .coin("USDT")
                .size(withdrawalAmount)
                .password(withdrawalPassword)
                .build();
        // execute withdrawal
        FtxWithdrawalResponse withdrawalResp = ftxService.withdrawal(apiKeyWrite, apiSecretWrite, requestBean);

        if(withdrawalResp == null)
            throw new PaymentException(ResponseStatus.Cryptocurrency_Handshake_Fail);

        Integer responseId = Optional.of(withdrawalResp).map(FtxWithdrawalResponse::getId).orElse(null);
        if(responseId != null){
            String withdrawalId = "FTX" + responseId;
            return CryptocurrencyExchangeWithdrawalResponse.builder()
                    .withdrawalId(withdrawalId)
                    .amount(withdrawalResp.getSize())
                    .fee(withdrawalResp.getFee())
                    .status(withdrawalResp.getStatus())
                    .note(withdrawalResp.getTag())
                    .merchantWalletCode(account.getCode())
                    .method(withdrawalResp.getMethod())
                    .build();
        }

        return null;
    }
}
