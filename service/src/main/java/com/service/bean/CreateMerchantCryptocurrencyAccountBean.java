package com.service.bean;


import dao.enums.CryptocurrencyAccountType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CreateMerchantCryptocurrencyAccountBean {
    @NotNull
    private Integer brandId;
    @NotBlank
    private String code;

    private String name;

    @NotBlank
    private String currencyCode;

    @NotNull
    private Integer exchangeAccountId;

    private CryptocurrencyAccountType walletType;

    private String walletAddress;

    private String trcWalletAddress;

    private String apiKey;
    private String apiSecret;

    private String apiKeyWrite;
    private String apiSecretWrite;

    private String password;

    @NotBlank
    private String modifiedBy;

}
