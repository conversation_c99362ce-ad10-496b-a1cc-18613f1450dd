package com.service.bean;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UpdateTransCryptoReceivedTransIdInputBean {
    @NotBlank
    private String transactionId;
    @NotBlank
    private String receivedTransId;
    @NotBlank
    private String modifiedBy;

    public UpdateTransCryptoReceivedTransIdInputBean(String transactionId, String receivedTransId) {
        this.transactionId = transactionId;
        this.receivedTransId = receivedTransId;
    }
}
