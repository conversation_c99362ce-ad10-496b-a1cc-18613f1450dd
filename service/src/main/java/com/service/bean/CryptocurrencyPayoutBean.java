package com.service.bean;

import com.service.enums.BitfinexDepositMethod;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@SuperBuilder
@Data
public class CryptocurrencyPayoutBean {
    @NotNull
    private Integer brandId;
    private boolean ignoreWebScraping;
    @NotNull
    private BigDecimal amount;
    @NotNull
    private BigDecimal chargeAmount;
    @NotBlank
    private String gamerOrderId;
    @NotBlank
    private String address;
    @NotNull
    private BitfinexDepositMethod method;
}
