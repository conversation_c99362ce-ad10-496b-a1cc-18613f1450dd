package com.service.bean;

import dao.bean.AgentRebateDetailUpdateStatusByIdBean;
import dao.enums.SettleStatus;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class UpdateAgentRebateDetailStatusByIdBean extends AgentRebateDetailUpdateStatusByIdBean {
    @Min(1)
    @Override
    public int getId() {
        return super.getId();
    }

    @NotNull
    @Override
    public SettleStatus getStatus() {
        return super.getStatus();
    }

    @NotBlank
    @Override
    public String getModifiedBy() {
        return super.getModifiedBy();
    }

    @NotNull
    @Override
    public Date getModifiedDate() {
        return super.getModifiedDate();
    }
}
