package com.service.bean;

import dao.bean.AgentRebateDetailUpdateStatusByCodeBean;
import dao.enums.SettleStatus;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class UpdateAgentRebateDetailStatusByCodeBean extends AgentRebateDetailUpdateStatusByCodeBean {
    @NotBlank
    @Override
    public String getCode() {
        return super.getCode();
    }

    @NotNull
    @Override
    public SettleStatus getStatus() {
        return super.getStatus();
    }

    @NotBlank
    @Override
    public String getModifiedBy() {
        return super.getModifiedBy();
    }

    @NotNull
    @Override
    public Date getModifiedDate() {
        return super.getModifiedDate();
    }

}
