package com.service.bean;

import dao.enums.CommonStatus;
import dao.enums.DepositUSDTProtocol;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetAccountWalletAddressAndUpdateProcessBean {
    @NotNull
    private Integer merchantAccountId;
    @NotNull
    private CommonStatus status;
    private DepositUSDTProtocol currency;
    private Integer retryTimes;

    public GetAccountWalletAddressAndUpdateProcessBean(@NotNull Integer merchantAccountId, @NotNull CommonStatus status) {
        this.merchantAccountId = merchantAccountId;
        this.status = status;
    }
}
