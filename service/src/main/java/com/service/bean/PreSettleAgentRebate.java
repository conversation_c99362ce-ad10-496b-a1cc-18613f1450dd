package com.service.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PreSettleAgentRebate {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    private int level;
    @JsonProperty(value = "brandID")
    private int brandId;

    public PreSettleAgentRebate() {
    }

    public PreSettleAgentRebate(Date startDate, Date endDate, int level) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.level = level;
    }
}
