package com.service.impl;

import com.bean.*;
import com.constant.CommonConstant;
import com.enums.ResponseStatus;
import com.exception.PaymentException;
import com.exception.PaymentGeneralException;
import com.service.PaymentProcessService;
import com.service.transaction.*;
import dao.bean.MerchantBankAccount;
import dao.bean.TransactionBean;
import dao.bean.TransactionReferenceBean;
import dao.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PaymentProcessServiceImpl implements PaymentProcessService {

    @Autowired
    TransactionService transactionService;

    @Autowired
    CreateTransactionService createTransactionService;

    @Autowired
    CreateAgentTransactionService createAgentTransactionService;

    @Autowired
    CommonTransactionService commonTransactionService;

    @Autowired
    UpdateTransactionStatusService updateTransactionStatusService;

    @Override
    public MerchantBankAccount getMerchantBankAccount(List<DepositMerchantBankAccount> merchantBankAccounts, BigDecimal amount) {
        // if all limit status is false then will use min totalDepositAmount bank account
        boolean limitStatusEnable = isLimitStatusEnable(merchantBankAccounts);
        Supplier<PaymentGeneralException> merchantBankAccountNotFoundException = () -> new PaymentGeneralException("amount:" + amount, ResponseStatus.MerchantBankAccount_NotFound);
        if(limitStatusEnable){
            // return max deposit amount merchant bank account
            return merchantBankAccounts.stream()
                    .max(Comparator.comparing(DepositMerchantBankAccount::getTotalDepositAmount))
                    .orElseThrow(merchantBankAccountNotFoundException);
        }else{
            // find min merchant bank account
            return merchantBankAccounts.stream()
                    .min(Comparator.comparing(DepositMerchantBankAccount::getTotalDepositAmount))
                    .orElseThrow(merchantBankAccountNotFoundException);
        }
    }

    // return list-> code | min | max | limitStatus
    private List<String> logDepositBankAccountList(List<DepositMerchantBankAccount> depositMerchantBankAccounts) {
        return depositMerchantBankAccounts.stream()
                .map(d -> String.join("|",
                        d.getCode(), d.getMinLimitAmount().toPlainString(), d.getMaxLimitAmount().toPlainString(), Boolean.toString(d.isLimitStatus())))
                .collect(Collectors.toList());
    }

    private boolean isLimitStatusEnable(List<DepositMerchantBankAccount> merchantBankAccounts) {
        return merchantBankAccounts.stream().anyMatch(MerchantBankAccount::isLimitStatus);
    }

    @Override
    public void validAdjustSnatchReferenceTransaction(TransactionBean transactionBean) {
        if(transactionBean == null){
            throw new PaymentException(ResponseStatus.Transaction_NotFound);
        }
        if(!isSnatchRejectTimeoutTransaction(transactionBean)){
            log.error("Violation reference transaction:{}/{}/{}, should be Snatch Rejected timeout",
                    transactionBean.getTransactionType(), transactionBean.getTransactionStatus(), transactionBean.getReason());
            throw new PaymentException(ResponseStatus.Reference_Transaction_Violation);
        }
        if(StringUtils.isNotBlank(transactionBean.getAdjTransId())){
            log.error("[{}]Transaction is Adjusted:{}", transactionBean.getTransactionId(), transactionBean.getAdjTransId());
            throw new PaymentException(ResponseStatus.Transaction_IsAdjusted);
        }
    }

    private boolean isSnatchRejectTimeoutTransaction(TransactionBean transactionBean) {
        return transactionBean.getTransactionType() == TransactionType.Snatch &&
                transactionBean.getTransactionStatus() == TransactionStatus.Rejected &&
                transactionBean.getReason() == Reason.TIME_OUT;
    }

    @Override
    public BigDecimal rollbackTransactionReferenceRebateAmount(List<TransactionReferenceBean> referenceBeanList, MemberType memberType, ReferenceType referenceType, String modifiedBy) {
        Optional<TransactionReferenceBean> rebateReference = referenceBeanList.stream()
                .filter(r -> r.getStatus() == CommonStatus.Active)
                .filter(r -> r.getMemberType() == memberType)
                .filter(r -> r.getReferenceType() == referenceType)
                .findFirst();
        if (rebateReference.isPresent()) {
            TransactionReferenceBean referenceBean = rebateReference.get();
            modifiedBy = Optional.ofNullable(modifiedBy).orElse(CommonConstant.MODIFIED_BY_SYSTEM);
            Redirector<TransactionDetails> result;
            if(MemberType.Member == memberType){
                // adjust deduct member referenceType amount
                result = adjustDeductMemberRebateAmount(referenceBean, modifiedBy);
            }else{
                // adjust deduct agent rebate amount
                result = adjustDeductAgentRebateAmount(referenceBean, modifiedBy);
            }
            // update reference status to inactive
            if (result != null) {
                String transactionId = referenceBean.getTransactionId();
                updateTransReferenceStatusToInactive(transactionId, modifiedBy);
                updateTransactionStatusToReject(transactionId, modifiedBy, false);
            }
            return Optional.ofNullable(result).map(Redirector::getNetAmount).orElse(null);
        }
        return null;
    }

    private Redirector<TransactionDetails> adjustDeductAgentRebateAmount(TransactionReferenceBean transactionReferenceBean, String modifiedBy) {
        String referenceTransId = transactionReferenceBean.getReferenceTransId();
        TransactionBean transactionBean = commonTransactionService.getTransactionByTransId(referenceTransId);
        Integer agentId = transactionBean.getAgentId();
        String currencyCode = transactionBean.getCurrencyCode();
        Reason reason = Reason.RollbackAdjust;
        BigDecimal reduceAmount = transactionReferenceBean.getAmount();

        log.info("[{}]Rollback adjust deduct agent({}) rebate amount:{}",
                referenceTransId, agentId, reduceAmount);
        // execute adjust deduct member rebate amount
        AgentAdjustTransactionInfoBean bean = new AgentAdjustTransactionInfoBean(currencyCode, agentId, reduceAmount);
        bean.setReason(reason);
        bean.setModifiedBy(modifiedBy);
        bean.setReferenceId(referenceTransId);
        return createAgentTransactionService.createAdjustAgentDeductRebate(bean);
    }

    private Redirector<TransactionDetails> adjustDeductMemberRebateAmount(TransactionReferenceBean transactionReferenceBean, String modifiedBy) {
        String referenceTransId = transactionReferenceBean.getReferenceTransId();
        String originalTransactionId = transactionReferenceBean.getTransactionId();
        TransactionBean transactionBean = commonTransactionService.getTransactionByTransId(originalTransactionId);
        Integer memberId = transactionBean.getMemberId();
        String currencyCode = transactionBean.getCurrencyCode();
        Reason reason = Reason.RollbackAdjust;
        BigDecimal reduceAmount = transactionReferenceBean.getAmount();

        log.info("[{}-{}]adjust deduct member({}) rebate amount:{}",
                referenceTransId, originalTransactionId, memberId, reduceAmount);
        // execute adjust deduct member rebate amount
        AdjustTransactionInfoBean bean = new AdjustTransactionInfoBean(currencyCode, memberId, reduceAmount);
        bean.setReason(reason);
        bean.setModifiedBy(modifiedBy);
        bean.setReferenceId(referenceTransId);
        return createTransactionService.createAdjustDeductRebate(bean);
    }

    @Override
    public BigDecimal rollbackSnatchAmount(String transactionId, String modifiedBy) {
        TransactionBean transactionBean = commonTransactionService.getTransactionByTransId(transactionId);
        // valid is Snatch/AdjustSnatch Approved transaction
        Optional.ofNullable(transactionBean)
                .filter(t -> t.getTransactionType() == TransactionType.Snatch
                        || t.getTransactionType() == TransactionType.AdjustSnatch
                        || t.getTransactionType() == TransactionType.AdjustSnatchDuplicatePay
                )
                .filter(t -> t.getTransactionStatus() == TransactionStatus.Approved)
                .orElseThrow(() -> new PaymentException(ResponseStatus.INVALID_DATA, "transaction type or status is invalid"));

        Integer memberId = transactionBean.getMemberId();
        String currencyCode = transactionBean.getCurrencyCode();
        Reason reason = Reason.RollbackAdjust;
        BigDecimal rollbackAmount = transactionBean.getAmount();
        modifiedBy = Optional.ofNullable(modifiedBy).orElse(CommonConstant.MODIFIED_BY_SYSTEM);

        log.info("[{}]rollback member({}) Snatch Amount:{}",
                transactionId, memberId, rollbackAmount);
        // execute rollback(add) member snatch amount
        AdjustTransactionInfoBean bean = new AdjustTransactionInfoBean(currencyCode, memberId, rollbackAmount);
        bean.setReason(reason);
        bean.setModifiedBy(modifiedBy);
        bean.setReferenceId(transactionId);
        Redirector<TransactionDetails> result = createTransactionService.createAdjustAddBalance(bean);
        if (result != null) {
            // update reference status to inactive
            updateTransReferenceStatusToInactive(transactionId, modifiedBy);
            // update transaction status to reject and reset adjustTransId,rebateTransId,agentRebateTransId to null
            updateTransactionStatusToReject(transactionId, modifiedBy, true);
        }
        return Optional.ofNullable(result).map(Redirector::getNetAmount).orElse(null);
    }

    private void updateTransactionStatusToReject(String transactionId, String modifiedBy, boolean resetTransDetailLog) {
        try {
            updateTransactionStatusService.transactionSnatchRollbackToReject(transactionId, modifiedBy, resetTransDetailLog);
        }catch(Exception e){
            log.error("[{}]transactionRollbackToReject fail.", transactionId, e);
        }
    }

    private void updateTransactionDepositStatusToReject(String transactionId, String modifiedBy) {
        try {
            updateTransactionStatusService.transactionDepositRollbackToReject(transactionId, modifiedBy);
        }catch(Exception e){
            log.error("[{}]transactionRollbackToReject fail.", transactionId, e);
        }
    }

    private void updateTransReferenceStatusToInactive(String transactionId, String modifiedBy) {
        try {
            transactionService.updateTransactionReferenceStatus(transactionId, CommonStatus.InActive, modifiedBy);
        } catch (Exception e) {
            log.error("[{}]updateTransactionReferenceStatus fail.", transactionId, e);
        }
    }

    @Override
    public BigDecimal rollbackDepositAmount(String transactionId, String modifiedBy) {
        TransactionBean transactionBean = commonTransactionService.getTransactionByTransId(transactionId);
        // valid is Deposit Approved transaction
        Optional.ofNullable(transactionBean)
                .filter(t -> t.getTransactionType() == TransactionType.Deposit)
                .filter(t -> t.getTransactionStatus() == TransactionStatus.Approved)
                .orElseThrow(() -> new PaymentException(ResponseStatus.INVALID_DATA, "transaction type or status is invalid"));

        Integer memberId = transactionBean.getMemberId();
        String currencyCode = transactionBean.getCurrencyCode();
        Reason reason = Reason.RollbackAdjust;
        BigDecimal rollbackAmount = transactionBean.getAmount();
        modifiedBy = Optional.ofNullable(modifiedBy).orElse(CommonConstant.MODIFIED_BY_SYSTEM);

        log.info("[{}]rollback member({}) Deposit Amount:{}",
                transactionId, memberId, rollbackAmount);
        // execute rollback(add) member deposit amount
        AdjustTransactionInfoBean bean = new AdjustTransactionInfoBean(currencyCode, memberId, rollbackAmount);
        bean.setReason(reason);
        bean.setModifiedBy(modifiedBy);
        bean.setReferenceId(transactionId);
        Redirector<TransactionDetails> result = createTransactionService.createAdjustDeductBalance(bean);
        if (result != null) {
            // update reference status to inactive
            updateTransReferenceStatusToInactive(transactionId, modifiedBy);
            // update transaction status to reject and reset adjustTransId,rebateTransId,agentRebateTransId to null
            updateTransactionDepositStatusToReject(transactionId, modifiedBy);
        }
        return Optional.ofNullable(result).map(Redirector::getNetAmount).orElse(null);
    }
}
