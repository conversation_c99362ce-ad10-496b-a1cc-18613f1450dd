package com.service.impl;

import com.enums.ResponseStatus;
import com.exception.PaymentException;
import com.google.protobuf.Int32Value;
import com.google.protobuf.StringValue;
import com.grpcclient.bean.MemberCurrentBalance;
import com.grpcclient.member.MemberTransactionService;
import com.service.BaseService;
import com.service.MemberRebateWalletService;
import com.tools.BigDecimalUtils;
import com.tools.DateTimeUtils;
import dao.bean.TransactionBean;
import dao.enums.Category;
import dao.enums.MemberType;
import dao.enums.TransactionType;
import gRPC.trans.Message.Transactionservice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.EnumSet;

@Service
public class MemberRebateWalletServiceImpl extends BaseService implements MemberRebateWalletService {

    @Autowired
    @Qualifier("memberTransactionServiceImpl")
    MemberTransactionService memberTransactionServiceGrpc;


    protected MemberCurrentBalance updateMemberRebateBalance(
            Transactionservice.gCreateRebateRequest.Builder builder, boolean forceUpdate){
        builder.setForceUpdateBalance(forceUpdate);
        return memberTransactionServiceGrpc.updateMemberRebateBalance(builder.build());
    }

    @Override
    public MemberCurrentBalance deductMemberRebateBalance(
            int memberId, BigDecimal amount, String transactionId, String modifiedBy,
            TransactionType refTransactionType, MemberType memberType, Category categoryId){

        Transactionservice.gCreateRebateRequest.Builder builder = Transactionservice.gCreateRebateRequest.newBuilder();
        builder.setAmount(BigDecimalUtils.init(amount).negate().doubleValue());
        builder.setAction(Transactionservice.gCreateTransRequest.TransactionAction.Deduct);
        builder.setMemberID(memberId);
//        getRequestAmount(amount, refTransactionType, builder);

        builder.setReferenceID(transactionId);
        builder.setCreatedBy(modifiedBy);
        getMemberType(memberType, builder);
        builder.setAction(Transactionservice.gCreateTransRequest.TransactionAction.Deduct);
//        getTransactionAction(refTransactionType, builder);
        Transactionservice.gRebateType memberRebateType = getMemberRebateType(categoryId);
        if(memberRebateType != null){
            builder.setRebateType(Int32Value.of(memberRebateType.getNumber()));
        }
        return updateMemberRebateBalance(builder, false);
    }

    private Transactionservice.gRebateType getMemberRebateType(Category categoryId){
        if(categoryId == null){
            return null;
        }
        switch (categoryId) {
            case WECHAT:
                return Transactionservice.gRebateType.WechatSnatch;
            case ALIPAY:
                return Transactionservice.gRebateType.AlipaySnatch;
            case ALIPAYH5:
                return Transactionservice.gRebateType.AlipayH5Snatch;
            case WECHAT_REWARD:
                return Transactionservice.gRebateType.WechatRewardSnatch;
            case ALIPAY_TO_ALIPAY:
                return Transactionservice.gRebateType.AlipayToAlipaySnatch;
            case ALIPAY_TO_BANK:
                return Transactionservice.gRebateType.AlipayToBankSnatch;
            case LOCAL_BANK_TRANSFER:
                return Transactionservice.gRebateType.IMPSSnatch;
            case LOCAL_BANK_TRANSFER_UPI:
                return Transactionservice.gRebateType.IndiaUPISnatch;
            case LOCAL_BANK_TRANSFER_UPI_H5:
                return Transactionservice.gRebateType.IndiaUPIH5Snatch;
            case DEPOSIT_POOL:
                return Transactionservice.gRebateType.Deposit;
            default:
                return null;
        }
    }

    private Transactionservice.gUpdateTransRequest.Builder initSettleMemberBalanceBean(TransactionBean bean) {
        return Transactionservice.gUpdateTransRequest.newBuilder()
                .setReferenceID(bean.getTransactionId())
                .setBrandID(bean.getBrandId())
                .setCreatedBy(bean.getModifiedBy())
                .setTransType(bean.getTransactionType().name())
                .setMemberID(bean.getMemberId())
                .setAmount(bean.getAmount().doubleValue())
                .setTransDate(DateTimeUtils.formatDate(bean.getTransactionDate()))
                ;
    }

    private void getRequestAmount(BigDecimal amount, TransactionType refTransactionType, Transactionservice.gCreateRebateRequest.Builder builder) {
        amount = BigDecimalUtils.init(amount);
        EnumSet<TransactionType> deductRebateTransTypes = EnumSet.of(TransactionType.RebateTransfer, TransactionType.Withdrawal, TransactionType.AdjustDeduct);
        if(deductRebateTransTypes.contains(refTransactionType)){
            amount = amount.abs().negate();
        }
        builder.setAmount(amount.doubleValue());
    }

    private void getTransactionAction(TransactionType refTransactionType, Transactionservice.gCreateRebateRequest.Builder builder) {
        Transactionservice.gCreateTransRequest.TransactionAction transactionAction = Transactionservice.gCreateTransRequest.TransactionAction.Default;
        if(refTransactionType == TransactionType.Withdrawal){
            transactionAction = Transactionservice.gCreateTransRequest.TransactionAction.Deduct;
        }
        builder.setAction(transactionAction);
    }

    @Override
    public MemberCurrentBalance adjustMemberRebateBalance(TransactionBean transactionBean){
        Transactionservice.gCreateRebateRequest.Builder builder = initRebateBuilder(transactionBean, transactionBean.getTransactionType(), transactionBean.getMemberType());
        return updateMemberRebateBalance(builder, true);
    }

    @Override
    public MemberCurrentBalance adjustMemberRebateBalance(
            int memberId, BigDecimal amount, String transactionId, String modifiedBy,
            TransactionType refTransactionType, MemberType memberType, Category categoryId){
        Transactionservice.gCreateRebateRequest.Builder builder = Transactionservice.gCreateRebateRequest.newBuilder();
        builder.setMemberID(memberId);
        getRequestAmount(amount, refTransactionType, builder);
        builder.setReferenceID(transactionId);
        builder.setCreatedBy(modifiedBy);
        getMemberType(memberType, builder);
        getTransactionAction(refTransactionType, builder);
        Transactionservice.gRebateType memberRebateType = getMemberRebateType(categoryId);
        if(memberRebateType != null){
            builder.setRebateType(Int32Value.of(memberRebateType.getNumber()));
        }
        return updateMemberRebateBalance(builder, true);
    }

    /**
     * deduct member rebate balance and make sure current balance is positive
     */
    @Override
    public MemberCurrentBalance directDeductMemberRebateBalance(
            int memberId, BigDecimal amount, String transactionId, String modifiedBy,
            TransactionType refTransactionType, MemberType memberType, Category categoryId){
        Transactionservice.gCreateRebateRequest.Builder builder = Transactionservice.gCreateRebateRequest.newBuilder();
        builder.setMemberID(memberId);
        builder.setAmount(amount.negate().doubleValue());
        builder.setReferenceID(transactionId);
        builder.setCreatedBy(modifiedBy);
        getMemberType(memberType, builder);
        getTransactionAction(refTransactionType, builder);
        Transactionservice.gRebateType memberRebateType = getMemberRebateType(categoryId);
        if(memberRebateType != null){
            builder.setRebateType(Int32Value.of(memberRebateType.getNumber()));
        }
        return updateMemberRebateBalance(builder, false);
    }

    private Transactionservice.gCreateRebateRequest.Builder initRebateBuilder(TransactionBean transactionBean, TransactionType refTransactionType, MemberType memberType) {
        Transactionservice.gCreateRebateRequest.Builder builder = Transactionservice.gCreateRebateRequest.newBuilder();
        builder.setMemberID(transactionBean.getMemberId());
        getRequestAmount(transactionBean.getAmount(), transactionBean.getTransactionType(), builder);
        builder.setReferenceID(transactionBean.getTransactionId());
        builder.setCreatedBy(transactionBean.getModifiedBy());
        getMemberType(memberType, builder);
        getTransactionAction(refTransactionType, builder);
        return builder;
    }

    private void getMemberType(MemberType memberType, Transactionservice.gCreateRebateRequest.Builder builder) {
        if(memberType == MemberType.Member){
            builder.setSourceType(Transactionservice.gCreateRebateRequest.RebateSource.Member);
        }else if(memberType == MemberType.Agent){
            builder.setSourceType(Transactionservice.gCreateRebateRequest.RebateSource.Agent);
        }else{
            logger.error("MemberType_Not_Supported:{}", memberType);
            throw new PaymentException(ResponseStatus.MemberType_Not_Supported);
        }
    }

    protected MemberCurrentBalance settleRebateBalance(Transactionservice.gUpdateRebate.Builder request){
        request.setForceUpdateBalance(false);

        return memberTransactionServiceGrpc.settleRebateBalance(request.build());
    }

    @Override
    public MemberCurrentBalance approveRebateBalance(String modifiedBy, TransactionBean bean){
        Transactionservice.gUpdateRebate.Builder request = Transactionservice.gUpdateRebate.newBuilder();
        initSettleRebateBean(request, modifiedBy, bean.getTransactionId(), bean.getBrandId(), bean.getMemberId(), bean.getMemberType());

        request.setStatus(Transactionservice.gUpdateTransRequest.TransactionStatus.Approve);
        return settleRebateBalance(request);
    }

    @Override
    public MemberCurrentBalance rejectRebateBalance(String modifiedBy, TransactionBean bean){
        Transactionservice.gUpdateRebate.Builder request = Transactionservice.gUpdateRebate.newBuilder();
        initSettleRebateBean(request, modifiedBy, bean.getTransactionId(), bean.getBrandId(), bean.getMemberId(), bean.getMemberType());

        request.setStatus(Transactionservice.gUpdateTransRequest.TransactionStatus.Reject);
        return settleRebateBalance(request);
    }

    private void initSettleRebateBean(Transactionservice.gUpdateRebate.Builder request, String modifiedBy,
                                      String transactionId, int brandId, int memberId, MemberType memberType) {
        request.setReferenceID(transactionId);
        request.setModifiedBy(StringValue.newBuilder().setValue(modifiedBy).build());
        request.setBrandID(brandId);
        request.setID(memberId);
        if(memberType == MemberType.Member){
            request.setType(Transactionservice.gCreateRebateRequest.RebateSource.Member);
        }else{
            request.setType(Transactionservice.gCreateRebateRequest.RebateSource.Agent);
        }
    }

}
