package com.service.impl;

import com.grpcclient.bean.MemberCurrentBalance;
import com.grpcclient.member.MemberCurrencyCode;
import com.grpcclient.member.MemberTransactionService;
import com.service.BaseService;
import com.service.MemberWalletService;
import com.tools.DateTimeUtils;
import dao.bean.TransactionBean;
import dao.enums.Category;
import dao.enums.TransactionType;
import gRPC.trans.Message.Transactionservice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class MemberWalletServiceImpl extends BaseService implements MemberWalletService {

    @Autowired
    @Qualifier("memberTransactionServiceImpl")
    MemberTransactionService memberTransactionServiceGrpc;

    /**
     * update balance and will check amount is negative
     */
    @Override
    public MemberCurrentBalance deductMemberBalance(int memberId, BigDecimal amount, TransactionType transactionType
            , String transactionId, String modifiedBy, MemberCurrencyCode memberCurrencyCode) {
        Transactionservice.gCreateTransRequest.TransactionAction transactionAction =
                Transactionservice.gCreateTransRequest.TransactionAction.Deduct;

        return memberTransactionServiceGrpc.updateMemberBalance(
                memberId, amount, transactionType.name(), transactionId, modifiedBy, transactionAction, false,
                Transactionservice.gRebateType.None, memberCurrencyCode);
    }

    /**
     * adjust balance no need check amount is negative
     */
    protected MemberCurrentBalance adjustMemberBalance(int memberId, BigDecimal amount, TransactionType transactionType,
                                                       String transactionId, String modifiedBy, boolean isDeduct, Category category, MemberCurrencyCode memberCurrencyCode) {
        Transactionservice.gCreateTransRequest.TransactionAction transactionAction = Transactionservice.gCreateTransRequest.TransactionAction.Default;
        if(isDeduct){
            transactionAction = Transactionservice.gCreateTransRequest.TransactionAction.Deduct;
        }
        amount = amount.abs();
        // Apply negation for transaction types that deduct balance
        if (isDeductTransactionType(transactionType) || 
            (transactionType == TransactionType.Transfer && category == Category.TRANSFER_OUT)) {
            amount = amount.negate();
        }

        Transactionservice.gRebateType rebateType = Transactionservice.gRebateType.None;
        if(isAdjustSnatchType(transactionType)){
            Transactionservice.gRebateType memberRebateType = getMemberRebateType(category);
            if(memberRebateType != null){
                rebateType = memberRebateType;
            }
        }
        if(TransactionType.Deposit == transactionType && category != null){
            rebateType = Transactionservice.gRebateType.Deposit;
        }
        return memberTransactionServiceGrpc.updateMemberBalance(
                memberId, amount, transactionType.name(), transactionId, modifiedBy, transactionAction, true, rebateType,
                memberCurrencyCode);
    }

    @Override
    public MemberCurrentBalance adjustMemberBalance(int memberId, BigDecimal amount, TransactionType transactionType,
                                                    String transactionId, String modifiedBy, Category category, MemberCurrencyCode currencyCode) {
        return adjustMemberBalance(memberId, amount, transactionType, transactionId, modifiedBy, false, category, currencyCode);
    }

    @Override
    public MemberCurrentBalance freezeMemberBalance(int memberId, BigDecimal amount, TransactionType transactionType,
                                                    String transactionId, String modifiedBy, Category category, MemberCurrencyCode currencyCode) {
        return adjustMemberBalance(memberId, amount, transactionType, transactionId, modifiedBy, true, category, currencyCode);
    }

    protected MemberCurrentBalance settleMemberBalanceForDeduct(Transactionservice.gUpdateTransRequest.Builder request, boolean forceUpdate, boolean isComplainTrans){
        request.setForceUpdateBalance(forceUpdate)
                .setCustomerComplaint(isComplainTrans);

        return memberTransactionServiceGrpc.settleMemberBalance(request.build());
    }

    /**
     * normal settle balance
     */
    protected MemberCurrentBalance settleMemberBalance(TransactionBean bean, boolean isApprove){
        Transactionservice.gUpdateTransRequest.Builder request = initSettleMemberBalanceBean(bean);

        request.setStatus(isApprove ?
                Transactionservice.gUpdateTransRequest.TransactionStatus.Approve:
                Transactionservice.gUpdateTransRequest.TransactionStatus.Reject);
        boolean forceUpdate = false;
        // snatch approve will log transSummary and no need check amount is negative
        if(bean.getTransactionType() == TransactionType.Snatch && isApprove){
            request.setLogTransSummary(true);
            forceUpdate = true;
        }else{
            // withdrawal no need log
            request.setLogTransSummary(false);
        }
        Category categoryId = bean.getCategoryId();
        Transactionservice.gRebateType memberRebateType = getMemberRebateType(categoryId);
        if(memberRebateType != null){
            request.setRebateType(memberRebateType);
        }

        return settleMemberBalanceForDeduct(request, forceUpdate, false);
    }

    @Override
    public MemberCurrentBalance approveMemberBalanceForDeduct(TransactionBean bean){
        return settleMemberBalance(bean, true);
    }

    @Override
    public MemberCurrentBalance rejectMemberBalanceForDeduct(TransactionBean bean){
        return settleMemberBalance(bean, false);
    }

    private Transactionservice.gRebateType getMemberRebateType(Category categoryId){
        if(categoryId == null){
            return null;
        }
        switch (categoryId) {
            case WECHAT:
                return Transactionservice.gRebateType.WechatSnatch;
            case ALIPAY:
                return Transactionservice.gRebateType.AlipaySnatch;
            case ALIPAYH5:
                return Transactionservice.gRebateType.AlipayH5Snatch;
            case WECHAT_REWARD:
                return Transactionservice.gRebateType.WechatRewardSnatch;
            case ALIPAY_TO_ALIPAY:
                return Transactionservice.gRebateType.AlipayToAlipaySnatch;
            case ALIPAY_TO_BANK:
                return Transactionservice.gRebateType.AlipayToBankSnatch;
            case LOCAL_BANK_TRANSFER:
                return Transactionservice.gRebateType.IMPSSnatch;
            case LOCAL_BANK_TRANSFER_UPI:
                return Transactionservice.gRebateType.IndiaUPISnatch;
            case LOCAL_BANK_TRANSFER_UPI_H5:
                return Transactionservice.gRebateType.IndiaUPIH5Snatch;
            default:
                return null;
        }
    }

    private Transactionservice.gUpdateTransRequest.Builder initSettleMemberBalanceBean(TransactionBean bean) {
        return Transactionservice.gUpdateTransRequest.newBuilder()
                    .setReferenceID(bean.getTransactionId())
                    .setBrandID(bean.getBrandId())
                    .setCreatedBy(bean.getModifiedBy())
                    .setTransType(bean.getTransactionType().name())
                    .setMemberID(bean.getMemberId())
                    .setAmount(bean.getAmount().doubleValue())
                    .setTransDate(DateTimeUtils.formatDate(bean.getTransactionDate()))
                ;
    }


    protected MemberCurrentBalance updateMemberRebateBalance(
            Transactionservice.gCreateRebateRequest.Builder builder, boolean forceUpdate){
        builder.setForceUpdateBalance(forceUpdate);
        return memberTransactionServiceGrpc.updateMemberRebateBalance(builder.build());
    }

    /**
     * Determines if a transaction type should deduct balance (negate amount)
     *
     * @param transactionType The transaction type to check
     * @return true if the transaction type should deduct balance, false otherwise
     */
    private boolean isDeductTransactionType(TransactionType transactionType) {
        return TransactionType.AdjustSnatch == transactionType
                || TransactionType.AdjustDeduct == transactionType
                || TransactionType.AdjustSnatchDuplicatePay == transactionType
                || TransactionType.ExchangeCurrency == transactionType
        ;
    }

    /**
     * Determines if a transaction type is an adjust snatch type that requires rebate type
     *
     * @param transactionType The transaction type to check
     * @return true if the transaction type is an adjust snatch type, false otherwise
     */
    private boolean isAdjustSnatchType(TransactionType transactionType) {
        return TransactionType.AdjustSnatch == transactionType ||
               TransactionType.AdjustSnatchDuplicatePay == transactionType;
    }
}
