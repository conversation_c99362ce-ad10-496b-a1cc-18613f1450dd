package com.service.transaction;

import com.bean.*;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class PaymentProcessFactory {

    private PaymentProcess<DepositTransactionInfoBean, DepositTransactionDetails> depositPaymentProcess;
    private PaymentProcess<WithdrawalTransactionInfoBean, TransactionDetails> withdrawalPaymentProcess;
    private PaymentProcess<AdjustTransactionInfoBean, TransactionDetails> adjustAddPaymentProcess;
    private PaymentProcess<AdjustTransactionInfoBean, TransactionDetails> adjustDeductPaymentProcess;
    private PaymentProcess<RebateTransactionInfoBean, TransactionDetails> rebatePaymentProcess;
    private PaymentProcess<SnatchInputBean, SnatchTransactionDetails> snatchPaymentProcess;
    private PaymentProcess<TransactionInfoBean, TransactionDetails> rebateTransferPaymentProcess;
    private PaymentProcess<AdjustSnatchTransactionInfoBean, TransactionDetails> adjustSnatchPaymentProcess;
    private PaymentProcess<TransactionInfoBean, TransactionDetails> freezePaymentProcess;
    private PaymentProcess<PromotionTransactionInfoBean, TransactionDetails> promotionPaymentProcess;
    private PaymentProcess<AdjustDuplicatePayoutTransactionInfoBean, TransactionDetails> adjustDuplicatePayoutPaymentProcess;
    private PaymentProcess<ReferralTransactionInfoBean, TransactionDetails> referralPaymentProcess;
    private PaymentProcess<AdjustSnatchDuplicatePayoutTransactionInfoBean, TransactionDetails> adjustSnatchDuplicatePayoutPaymentProcess;

    private PaymentProcess<DepositCryptocurrencyTransactionInfoBean, DepositCryptoTransactionDetails> depositCryptocurrencyPaymentProcess;
    private PaymentProcess<MscDepositTransactionInfoBean, TransactionDetails> mscDepositPaymentProcess;
    private PaymentProcess<ExchangeCurrencyTransactionInfoBean, ExchangeTransactionDetails> exchangeCurrencyPaymentProcess;
    private PaymentProcess<SystemPromotionTransactionInfoBean, TransactionDetails> systemPromotionPaymentProcess;
    private PaymentProcess<TransferInTransactionInfoBean, TransactionDetails> transferInPaymentProcess;
    private PaymentProcess<TransferOutTransactionInfoBean, TransactionDetails> transferOutPaymentProcess;
    private PaymentProcess<WithdrawalCryptocurrencyTransactionInfoBean, TransactionDetails> withdrawalCryptocurrencyPaymentProcess;

    @Autowired
    public void setDepositPaymentProcess(
            PaymentProcess<DepositTransactionInfoBean, DepositTransactionDetails> depositPaymentProcess) {
        this.depositPaymentProcess = depositPaymentProcess;
    }

    @Autowired
    public void setWithdrawalPaymentProcess(
            PaymentProcess<WithdrawalTransactionInfoBean, TransactionDetails> withdrawalPaymentProcess) {
        this.withdrawalPaymentProcess = withdrawalPaymentProcess;
    }

    @Autowired
    public void setAdjustAddPaymentProcess(
            PaymentProcess<AdjustTransactionInfoBean, TransactionDetails> adjustAddPaymentProcess) {
        this.adjustAddPaymentProcess = adjustAddPaymentProcess;
    }

    @Autowired
    public void setAdjustDeductPaymentProcess(
            PaymentProcess<AdjustTransactionInfoBean, TransactionDetails> adjustDeductPaymentProcess) {
        this.adjustDeductPaymentProcess = adjustDeductPaymentProcess;
    }

    @Autowired
    public void setRebatePaymentProcess(
            PaymentProcess<RebateTransactionInfoBean, TransactionDetails> rebatePaymentProcess) {
        this.rebatePaymentProcess = rebatePaymentProcess;
    }

    @Autowired
    public void setSnatchPaymentProcess(PaymentProcess<SnatchInputBean, SnatchTransactionDetails> snatchPaymentProcess) {
        this.snatchPaymentProcess = snatchPaymentProcess;
    }

    @Autowired
    public void setRebateTransferPaymentProcess(
            PaymentProcess<TransactionInfoBean, TransactionDetails> rebateTransferPaymentProcess) {
        this.rebateTransferPaymentProcess = rebateTransferPaymentProcess;
    }

    @Autowired
    public void setAdjustSnatchPaymentProcess(
            PaymentProcess<AdjustSnatchTransactionInfoBean, TransactionDetails> adjustSnatchPaymentProcess) {
        this.adjustSnatchPaymentProcess = adjustSnatchPaymentProcess;
    }

    @Autowired
    public void setFreezePaymentProcess(PaymentProcess<TransactionInfoBean, TransactionDetails> freezePaymentProcess) {
        this.freezePaymentProcess = freezePaymentProcess;
    }

    @Autowired
    public void setPromotionPaymentProcess(
            PaymentProcess<PromotionTransactionInfoBean, TransactionDetails> promotionPaymentProcess) {
        this.promotionPaymentProcess = promotionPaymentProcess;
    }

    @Autowired
    public void setAdjustDuplicatePayoutPaymentProcess(
            PaymentProcess<AdjustDuplicatePayoutTransactionInfoBean, TransactionDetails> adjustDuplicatePayoutPaymentProcess) {
        this.adjustDuplicatePayoutPaymentProcess = adjustDuplicatePayoutPaymentProcess;
    }

    @Autowired
    public void setDepositCryptocurrencyPaymentProcess(
            PaymentProcess<DepositCryptocurrencyTransactionInfoBean, DepositCryptoTransactionDetails> depositCryptocurrencyPaymentProcess) {
        this.depositCryptocurrencyPaymentProcess = depositCryptocurrencyPaymentProcess;
    }

    @Autowired
    public void setReferralPaymentProcess(
            PaymentProcess<ReferralTransactionInfoBean, TransactionDetails> referralPaymentProcess) {
        this.referralPaymentProcess = referralPaymentProcess;
    }

    @Autowired
    public void setAdjustSnatchDuplicatePayoutPaymentProcess(
            PaymentProcess<AdjustSnatchDuplicatePayoutTransactionInfoBean, TransactionDetails> adjustSnatchDuplicatePayoutPaymentProcess) {
        this.adjustSnatchDuplicatePayoutPaymentProcess = adjustSnatchDuplicatePayoutPaymentProcess;
    }

    @Autowired
    public void setMscDepositPaymentProcess(
            PaymentProcess<MscDepositTransactionInfoBean, TransactionDetails> mscDepositPaymentProcess) {
        this.mscDepositPaymentProcess = mscDepositPaymentProcess;
    }

    @Autowired
    public void setExchangeCurrencyPaymentProcess(
            PaymentProcess<ExchangeCurrencyTransactionInfoBean, ExchangeTransactionDetails> exchangeCurrencyPaymentProcess) {
        this.exchangeCurrencyPaymentProcess = exchangeCurrencyPaymentProcess;
    }

    @Autowired
    public void setSystemPromotionPaymentProcess(
            PaymentProcess<SystemPromotionTransactionInfoBean, TransactionDetails> systemPromotionPaymentProcess) {
        this.systemPromotionPaymentProcess = systemPromotionPaymentProcess;
    }

    @Autowired
    public void setTransferInPaymentProcess(PaymentProcess<TransferInTransactionInfoBean, TransactionDetails> transferInPaymentProcess) {
        this.transferInPaymentProcess = transferInPaymentProcess;
    }

    @Autowired
    public void setTransferOutPaymentProcess(PaymentProcess<TransferOutTransactionInfoBean, TransactionDetails> transferOutPaymentProcess) {
        this.transferOutPaymentProcess = transferOutPaymentProcess;
    }

    @Autowired
    public void setWithdrawalCryptocurrencyPaymentProcess(
            PaymentProcess<WithdrawalCryptocurrencyTransactionInfoBean, TransactionDetails> withdrawalCryptocurrencyPaymentProcess) {
        this.withdrawalCryptocurrencyPaymentProcess = withdrawalCryptocurrencyPaymentProcess;
    }
}
