package com.service.transaction.process;

import com.bean.ExchangeRateInfoBean;
import com.bean.Redirector;
import com.bean.TransactionDetails;
import com.bean.TransactionInfoBean;
import com.enums.ResponseStatus;
import com.exception.InsufficientBalanceException;
import com.exception.PaymentException;
import com.exception.PaymentGeneralException;
import com.grpcclient.bean.MemberCurrentBalance;
import com.grpcclient.exchangerate.ExchangeRateServiceGrpc;
import com.grpcclient.member.MemberCurrencyCode;
import com.grpcclient.paymentType.PaymentTypeServiceGrpc;
import com.grpcclient.transaction.TransactionDetailServiceGrpc;
import com.grpcclient.transaction.TransactionServiceGrpc;
import com.service.*;
import com.service.bean.AgentInfo;
import com.service.bean.CreateTransactionPromotionBean;
import com.service.bean.MemberDetail;
import com.service.bean.TransactionCommentCreateBean;
import com.service.transaction.*;
import com.tool.BeanValidator;
import com.tools.BigDecimalUtils;
import com.tools.JsonUtils;
import dao.bean.*;
import dao.enums.CommentType;
import dao.enums.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

public abstract class AbstractPaymentProcess<T extends TransactionInfoBean, U extends TransactionDetails> implements PaymentProcess<T,U> {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    public static List<TransactionType> preDeductTransactionTypes =
            Arrays.asList(TransactionType.Withdrawal, TransactionType.Snatch, TransactionType.Freeze);

    @Autowired
    BeanValidator beanValidator;

    @Autowired
    TransactionServiceGrpc transactionServiceGrpc;

    @Autowired
    TransactionDetailServiceGrpc transactionDetailServiceGrpc;

    @Autowired
    MemberService memberService;

    @Autowired
    AgentService agentService;

    @Autowired
    MemberWalletService memberWalletService;

    @Autowired
    MemberRebateWalletService memberRebateWalletService;

    @Autowired
    PaymentTypeServiceGrpc paymentTypeServiceGrpc;

    @Autowired
    TransactionService transactionService;

    @Autowired
    UpdateTransactionStatusProcess updateTransactionStatusProcess;

    @Autowired
    ExchangeRateServiceGrpc exchangeRateServiceGrpc;

    @Autowired
    UpdateTransactionStatusService updateTransactionStatusService;

    @Autowired
    TransactionCommentService transactionCommentService;

    @Autowired
    CommentTypeService commentTypeService;

    @Override
    public Redirector<U> executeMember(T bean){
        bean.setMemberType(MemberType.Member);
        MemberDetail memberDetail = initMemberData(bean.getMemberId(), bean.getBrandId(), bean.getMemberCode(), bean);
        validMemberDetail(memberDetail, bean);
        if(checkHoldWithdrawalStatus()){
            if(memberDetail.getHoldWithdrawal()){
                throw new PaymentException(ResponseStatus.Member_Hold_Withdrawal);
            }
        }
        if(isCheckMemberBalance()){
            if(bean.getCurrencyCode().equalsIgnoreCase(Currency.USDT.name())){
                validWithdrawalBalance(bean.getAmount(), BigDecimalUtils.init(memberDetail.getAvailableUsdtBalance()), bean.getMemberCode());
            }else{
                validWithdrawalBalance(bean.getAmount(), BigDecimalUtils.init(memberDetail.getAvailableBalance()), bean.getMemberCode());
            }
        }
        if(isCheckMemberRebateBalance()){
            validWithdrawalBalance(bean.getAmount(), BigDecimalUtils.init(memberDetail.getRebateBalance()), bean.getMemberCode());
        }
        return execute(bean);
    }

    protected boolean isCheckMemberRebateBalance() {
        return false;
    }

    protected boolean checkHoldWithdrawalStatus() {
        return false;
    }

    protected boolean isCheckMemberBalance() {
        return false;
    }

    @Override
    public Redirector<U> executeAgent(T bean){
        if(bean.getAgentId() == 0){
            throw new PaymentException(ResponseStatus.INVALID_DATA, "agentId cannot allow 0");
        }
        bean.setMemberType(MemberType.Agent);
        AgentInfo agentInfo = initAgentData(bean.getAgentId(), bean);
        validAgentDetails(agentInfo, bean);
        if(checkHoldWithdrawalStatus()){
            if(agentInfo.getAgent().getHoldWithdrawal()){
                throw new PaymentException(ResponseStatus.Agent_Hold_Withdrawal);
            }
        }
        if(isCheckAgentBalance()){
            validWithdrawalBalance(bean.getAmount(), BigDecimalUtils.init(agentInfo.getRebateBalance()), bean.getMemberCode());
        }
        return execute(bean);
    }

    protected boolean isCheckAgentBalance() {
        return false;
    }

    // default no check
    protected void validMemberDetail(MemberDetail memberDetail, T bean){}

    // default no check
    protected void validAgentDetails(AgentInfo agentInfo, T bean) {}

    private MemberDetail initMemberData(Integer memberId, Integer brandId, String memberCode, T bean) {
        if(memberId == null && (brandId == null || StringUtils.isBlank(memberCode))){
            logger.error("memberId/(brandId+memberCode) must not null");
            throw new PaymentException(ResponseStatus.Member_Not_Exist);
        }
        MemberDetail memberDetail;
        if(memberId != null){
            memberDetail = memberService.getMemberDetailByMemberId(memberId);
        }else{
            memberDetail = memberService.getMemberDetailByMemberCode(memberCode, brandId);
        }
        if(StringUtils.isBlank(bean.getCurrencyCode())){
            logger.error("currencyCode must not null");
            throw new PaymentException(ResponseStatus.INVALID_DATA, "currencyCode must not null");
        }

        bean.setBrandId(memberDetail.getBrandID());
        bean.setMemberCode(memberDetail.getMemberCode());
        bean.setMemberId(memberDetail.getMemberID());
        bean.setAgentId(memberDetail.getAgentID());
        if(bean.getCurrencyCode().equalsIgnoreCase(Currency.USDT.name())){
            bean.setActualBalance(BigDecimalUtils.init(memberDetail.getActualUsdtBalance()));
            bean.setAvailableBalance(BigDecimalUtils.init(memberDetail.getAvailableUsdtBalance()));
        }else {
            bean.setActualBalance(BigDecimalUtils.init(memberDetail.getActualBalance()));
            bean.setAvailableBalance(BigDecimalUtils.init(memberDetail.getAvailableBalance()));
        }
        bean.setIpAddress(memberDetail.getIp());
        bean.setDevice(memberDetail.getDeviceName());
        bean.setMemberName(memberDetail.getName());
        bean.setMemberWalletAddress(memberDetail.getWalletAddress());
        return memberDetail;
    }

    private AgentInfo initAgentData(Integer agentId, T bean) {
        AgentInfo agentInfo = agentService.getAgentInfo(agentId);
        if(agentInfo == null){
            throw new PaymentException(ResponseStatus.Agent_Not_Found);
        }
        AgentInfo.Agent agents = agentInfo.getAgent();
        bean.setMemberId(agents.getId());
        bean.setMemberCode(agents.getCode());
        bean.setBrandId(agents.getBrandID());
        bean.setAgentId(agents.getId());

        BigDecimal currentBonus = agentInfo.getRebateBalance();
        // agent only have rebate bonus
        bean.setActualBalance(currentBonus);
        bean.setAvailableBalance(currentBonus);
        bean.setMemberType(MemberType.Agent);
        return agentInfo;
    }

    @Override
    public Redirector<U> execute(T bean){
    	initTransactionTypeStatus(bean);
        logger.info("execute payment process : {}, member:{}, memberType:{}",
                bean.getTransactionType(), bean.getMemberCode(), bean.getMemberType());
        
        return executeProcess(bean);
    }

    abstract protected void initTransactionTypeStatus(T bean);

    private Redirector<U> executeProcess(T bean) {
        executePreProcess(bean);

        beanValidation(bean);

        getPaymentType(bean);

        getPaymentTypeLimit(bean);

        if(isCryptocurrencyToExchange()) {
            getExchangeRate(bean);
            cryptocurrencyToBaseCurrency(bean);
        }

        ruleCheck(bean);

        registerTransaction(bean);

        // deduct balance
        deductBalance(bean);


        executePostProcess(bean);

        // combine return data
        return getRedirector(bean);
    }

    private void logTransactionComment(T bean) {
        String transactionId = bean.getTransactionId();
        TransactionBean transactionBean = searchTransactionByTransId(transactionId);
        logger.info("logTransactionComment: transactionBean.getMemberCurrentAmount():{}", transactionBean.getMemberCurrentAmount());
        if(bean.getMemberType() == MemberType.Member && BigDecimalUtils.ltZero(transactionBean.getMemberCurrentAmount())){
            try {
                TransactionCommentCreateBean createBean = new TransactionCommentCreateBean(transactionId);
                CommentType commentType = CommentType.MemberBalanceNegative;
                dao.bean.CommentType commentTypeVo = commentTypeService.getExternalCommentTypeByCommentTypeId(commentType.getValue());
                if(commentTypeVo == null){
                    throw new PaymentException(ResponseStatus.CommentId_NotFound);
                }
                createBean.setCommentTypeId(commentTypeVo.getId());
                createBean.setComment(commentTypeVo.getCommentTypeName());
                createBean.setModifiedBy(bean.getModifiedBy());
                createBean.setAmount(transactionBean.getMemberCurrentAmount());
                transactionCommentService.createTransactionComment(createBean);
            }catch(Exception e){
                logger.error("[{}]create MemberBalanceNegative transaction comment fail.{}", transactionId, e.getMessage());
            }
        }

    }

    // setCurrencyCode/setAmount/setChargeAmount
    private void cryptocurrencyToBaseCurrency(T bean){
        // currency/amount/chargeAmount need convert to BASE(CNY) type
        ExchangeRateInfoBean exchangeRateInfoBean = bean.getExchangeRateInfoBean();

        bean.setCurrencyCode(exchangeRateInfoBean.getTargetCurrencyCode());
        bean.setAmount(exchangeRateInfoBean.getTargetAmount().setScale(4, RoundingMode.HALF_DOWN));
        BigDecimal chargeAmount = setCryptocurrencyChargeAmount(bean, exchangeRateInfoBean);
        bean.setChargeAmount(chargeAmount.setScale(4, RoundingMode.HALF_DOWN));
    }

    protected BigDecimal setCryptocurrencyChargeAmount(T bean, ExchangeRateInfoBean exchangeRateInfoBean) {
        BigDecimal chargeAmount = bean.getChargeAmount();
        if(BigDecimalUtils.ne(chargeAmount, BigDecimal.ZERO)){
            BigDecimal exchangeRate = exchangeRateInfoBean.getExchangeRate();
            return BigDecimalUtils.multiply(chargeAmount, exchangeRate);
        }
        return BigDecimal.ZERO;
    }

    // setExchangeRateInfoBean
    private void getExchangeRate(T bean) {
        String baseCurrencyCode = bean.getCurrencyCode();
        BigDecimal baseAmount = bean.getAmount();
        String targetCurrencyCode = "CNY";

        BigDecimal exchangeRateValue = getExchangeRateValue(bean, targetCurrencyCode);
        BigDecimal targetCurrencyAmount = BigDecimalUtils.multiply(bean.getAmount(), exchangeRateValue);

        // set others exchange rate info for store
        ExchangeRateInfoBean exchangeRateInfoBean = new ExchangeRateInfoBean(
                baseCurrencyCode, baseAmount,
                exchangeRateValue, targetCurrencyCode, targetCurrencyAmount
        );
        bean.setExchangeRateInfoBean(exchangeRateInfoBean);
    }

    protected BigDecimal getExchangeRateValue(T bean, String targetCurrencyCode) {
        // get exchangeRate by currency
        Integer brandId = bean.getBrandId();
        String fromCurrencyCode = bean.getCurrencyCode();
        ExchangeRateFilterBean filterBean = new ExchangeRateFilterBean(brandId, fromCurrencyCode, targetCurrencyCode);
        ExchangeRate exchangeRate = exchangeRateServiceGrpc.searchExchangeRateByKey(filterBean);
        if(exchangeRate == null){
            throw new PaymentException(ResponseStatus.ExchangeRate_NotFound);
        }

        return exchangeRate.getExchangeRate();
    }

    private void deductBalance(T bean) {
        DeductType deductType = isPreDeductBalance();
        if(deductType == DeductType.None){
            return;
        }
        String transactionId = bean.getTransactionId();
        logger.info("[{}] start to deduct member:{} balance...", transactionId, bean.getMemberCode());
        MemberCurrentBalance memberCurrentBalance;
        try{
            switch (deductType){
                case PreDeduct:
                    memberCurrentBalance = preDeductBalance(bean);
                    break;
                case DirectDeduct:
                    memberCurrentBalance = directDeductBalance(bean);
                    break;
                case Freeze:
                    memberCurrentBalance = freezeBalance(bean);
                    break;
                default:
                    logger.error("[{}]UnSupport DeductType:{}, ignore deduct balance.", transactionId, deductType);
                    return;
            }
        }catch(InsufficientBalanceException e){
            logger.error("member insufficient balance. update transaction status to insufficientBalance.", e);
            updateTransactionStatusProcess.insufficientBalanceError(transactionId);
            throw new PaymentException(ResponseStatus.Member_Insufficient_Balance);
        }catch(Exception e){
            logger.error("deductBalance fail. update transaction status to fail.", e);
            updateTransactionStatusProcess.transactionSystemError(transactionId, false);
            throw new PaymentException(ResponseStatus.Member_DeductBalance_Fail);
        }

        logger.info("[{}] deduct member:{} balance SUCCESS, current balance:{}", transactionId, bean.getMemberCode(), memberCurrentBalance);
        // update member current balance
        if(Optional.ofNullable(memberCurrentBalance).map(f->f.getAvailableBalance()).isPresent()) {
            TransactionDetailUpdateBean transactionDetailUpdateBean = new TransactionDetailUpdateBean();
            transactionDetailUpdateBean.setTransactionId(transactionId);
            transactionDetailUpdateBean.setMemberCurrentAmount(memberCurrentBalance.getAvailableBalance());
            transactionDetailUpdateBean.setModifiedDate(new Date());
            transactionDetailUpdateBean.setModifiedBy(bean.getModifiedBy());
            transactionDetailServiceGrpc.updateTransactionDetail(transactionDetailUpdateBean);
        }
    }

    // freeze used

    private MemberCurrentBalance freezeBalance(T bean) {
        return memberWalletService.freezeMemberBalance(bean.getMemberId(), bean.getAmount(),
                bean.getTransactionType(), bean.getTransactionId(), bean.getModifiedBy(), bean.getCategoryId(), MemberCurrencyCode.getEnumByCurrencyCode(bean.getCurrencyCode()));
    }
    // transfer used

    private MemberCurrentBalance directDeductBalance(T bean) {
        MemberCurrentBalance memberCurrentBalance = memberRebateWalletService.directDeductMemberRebateBalance(
                bean.getMemberId(), bean.getAmount(), bean.getTransactionId(), bean.getModifiedBy(),
                bean.getTransactionType(), MemberType.Member, bean.getCategoryId());
        memberCurrentBalance.setAvailableBalance(memberCurrentBalance.getRebateAmount());
        memberCurrentBalance.setActualBalance(memberCurrentBalance.getRebateAmount());
        return memberCurrentBalance;
    }

    protected void approveTransaction(String transactionId, String modifiedBy) {
        updateTransactionStatusService.transactionApprove(transactionId, modifiedBy, null, null);
    }


    protected enum DeductType{
        None,
        PreDeduct,
        DirectDeduct,
        Freeze,
        ;
    }
    protected DeductType isPreDeductBalance() {
        return DeductType.None;
    }

    private void ruleCheck(T bean){
        // check amount > 0
        if(BigDecimalUtils.le(bean.getAmount(), BigDecimal.ZERO)){
            throw new PaymentException(ResponseStatus.Transaction_Amount_ZERO_Violation);
        }

//        bean.setNetAmount(BigDecimalUtils.subtract(bean.getAmount(), bean.getChargeAmount()));

        //TODO refactor it, not use category
        //local bank transfer bean check
        if(isLocalBankTransfer()){
            beanValidator.beanValid(bean.getLocalBankTransfer());
        }
        if(isCryptocurrencyToExchange()){
            BeanUtils.copyProperties(bean.getExchangeRateInfoBean(), bean.getCryptocurrencyInfoBean());
            beanValidator.beanValid(bean.getCryptocurrencyInfoBean());
        }
    }

    private void getPaymentTypeLimit(T bean) {
        //get payment type limit setting
        int paymentTypeId = bean.getPaymentTypeId();
        String currencyCode = bean.getCurrencyCode();
        PaymentTypeLimit paymentTypeLimit = paymentTypeServiceGrpc.searchPaymentTypeLimit(paymentTypeId, currencyCode);
        if(paymentTypeLimit == null)
            return;

        if(!bypassPaymentTypeLimitCheck(bean)){
            if(BigDecimalUtils.lt(bean.getAmount(), paymentTypeLimit.getMinAmount())){
                logger.error("transaction amount:{} violation min amount:{}", bean.getAmount(), paymentTypeLimit.getMinAmount());
                throw new PaymentException(ResponseStatus.PaymentTypeLimit_Min_Violation, paymentTypeLimit.getMinAmount());
            }else if(BigDecimalUtils.gt(bean.getAmount(), paymentTypeLimit.getMaxAmount())){
                logger.error("transaction amount:{} violation max amount:{}", bean.getAmount(), paymentTypeLimit.getMaxAmount());
                throw new PaymentException(ResponseStatus.PaymentTypeLimit_Max_Violation, paymentTypeLimit.getMaxAmount());
            }
        }
        BigDecimal chargeAmount = getChargeAmount(bean.getAmount(), paymentTypeLimit);
        bean.setChargeAmount(chargeAmount);
        bean.setExpiredTimeSeconds(paymentTypeLimit.getExpiredTime());
    }

    protected boolean bypassPaymentTypeLimitCheck(T bean){
        return false;
    }

    private BigDecimal getChargeAmount(BigDecimal amount, PaymentTypeLimit paymentTypeLimit) {
        BigDecimal chargeAmount;
        // default type: Percent
        ChargeType chargeType = Optional.ofNullable(paymentTypeLimit.getChargeType()).orElse(ChargeType.Percent);
        if(chargeType == ChargeType.Percent){
            chargeAmount = BigDecimalUtils.multiply(amount, paymentTypeLimit.getChargePercent());
        }else{
            chargeAmount = paymentTypeLimit.getChargePercent();
        }
        return chargeAmount;
    }

    private Redirector<U> getRedirector(T bean) {
        TransactionBean transactionBean = searchTransactionByTransId(bean.getTransactionId());
        Redirector<U> redirector = new Redirector<>();
        redirector.setTransactionId(transactionBean.getTransactionId());
        redirector.setTransactionDate(transactionBean.getTransactionDate());
        redirector.setAmount(transactionBean.getAmount());
        redirector.setChargeAmount(transactionBean.getChargeAmount().negate());
        redirector.setNetAmount(transactionBean.getNetAmount());
        redirector.setTransactionStatus(transactionBean.getTransactionStatus());
        redirector.setCurrentBalance(transactionBean.getMemberCurrentAmount());
        redirector.setCurrency(transactionBean.getCurrencyCode());

        // do something
        setTransactionDetails(bean, redirector);

        logger.info("[{}] transaction getRedirector process done:{}.", bean.getTransactionId(), JsonUtils.toJson(redirector));
        return redirector;
    }

    protected void setTransactionDetails(T bean, Redirector<U> redirector) {
    }


    private void getPaymentType(T bean) {
        Category category = bean.getCategoryId();
        if(isLocalBankTransfer()){
            category = Category.LOCAL_BANK_TRANSFER;
        }else if(isCryptocurrencyToExchange()){
            category = Category.USDT;
        }
        // call grpc client to get payment type
        PaymentTypeFilter paymentTypeFilter = initPaymentTypeFilter(bean, category);
        List<PaymentType> paymentTypes = paymentTypeServiceGrpc.getPaymentType(paymentTypeFilter);
        logger.info("[getPaymentType({})] CategoryId:{}, result payment types:{}",
                paymentTypeFilter.getTransactionType(), paymentTypeFilter.getCategoryId(),
                paymentTypes.stream().map(PaymentType::getPaymentTypeCode).toArray());
        if(paymentTypes.size() != 1){
            throw new PaymentException(ResponseStatus.PaymentType_NotFound);
        }
        PaymentType paymentType = paymentTypes.get(0);

        bean.setPaymentTypeId(paymentType.getPaymentTypeId());
        bean.setPaymentGatewayId(paymentType.getPaymentGatewayId());
        bean.setTransactionPrefix(paymentType.getTransactionPrefix());
        bean.setCategoryId(paymentType.getCategoryId());
    }

    private PaymentTypeFilter initPaymentTypeFilter(T bean, Category category) {
        PaymentTypeFilter paymentTypeFilter = new PaymentTypeFilter();
        paymentTypeFilter.setBrandId(bean.getBrandId());
        paymentTypeFilter.setTransactionType(bean.getTransactionType());
        paymentTypeFilter.setCategoryId(category);
        paymentTypeFilter.setPaymentGatewayType(bean.getPaymentGatewayType());

        return paymentTypeFilter;
    }

    private void executePostProcess(T bean) {
        long endTime = System.currentTimeMillis();
        long executeTime = endTime - bean.getStartTime();
        logger.info("[{}]transaction start post process", bean.getTransactionId());
        postProcess(bean);
        long postProcessExecuteTime = System.currentTimeMillis() - endTime;
        logger.info("[{}] transaction process time:{} ms, post process executeTime:{} ms", bean.getTransactionId(), executeTime, postProcessExecuteTime);

        if(isLogTransactionComment()) {
            logTransactionComment(bean);
        }
    }

    protected boolean isLogTransactionComment(){
        return false;
    }

    abstract protected void postProcess(T bean);

    private void executePreProcess(T bean) {
        bean.setStartTime(System.currentTimeMillis());
        preProcess(bean);
    }

    abstract protected void preProcess(T bean);

    private MemberCurrentBalance preDeductBalance(T bean) {
        MemberCurrentBalance memberCurrentBalance;
        if(bean.getMemberType() == MemberType.Member){
            memberCurrentBalance = memberWalletService.deductMemberBalance(bean.getMemberId(), bean.getAmount(),
                    bean.getTransactionType(), bean.getTransactionId(), bean.getModifiedBy(),
                    MemberCurrencyCode.getEnumByCurrencyCode(bean.getCurrencyCode()));
        }else{
            memberCurrentBalance = memberRebateWalletService.deductMemberRebateBalance(
                    bean.getMemberId(), bean.getAmount(), bean.getTransactionId(), bean.getModifiedBy(),
                    bean.getTransactionType(), bean.getMemberType(), bean.getCategoryId());
            memberCurrentBalance.setAvailableBalance(memberCurrentBalance.getRebateAmount());
            memberCurrentBalance.setActualBalance(memberCurrentBalance.getRebateAmount());
        }
        return memberCurrentBalance;
    }

    private void registerTransaction(T bean) {
        String randomNumber = transactionService.getSequenceNumber(3);
        long currentTimeMillis = System.currentTimeMillis();
        String transactionNumber = currentTimeMillis + "" + randomNumber;
        bean.setTransactionId(bean.getTransactionPrefix() + transactionNumber);
        logger.info("perform register transaction:{}, member:{}", bean.getTransactionId(), bean.getMemberCode());

        // init reason remark
        Reason reason = bean.getReason() == null ? Reason.NO_REASON : bean.getReason();
        bean.setReason(reason);
        bean.setRemarks(StringUtils.isBlank(bean.getRemarks()) ? reason.getDescription() : bean.getRemarks());

        // transaction init
        Date now = new Date();
        bean.setTransactionDate(now);
        bean.setModifiedDate(now);
        bean.setModifiedBy(StringUtils.isBlank(bean.getModifiedBy()) ? bean.getMemberCode() : bean.getModifiedBy());
        bean.setStatusModifiedDate(now);
        bean.setNetAmount(BigDecimalUtils.subtract(bean.getAmount(), bean.getChargeAmount()).setScale(4, RoundingMode.HALF_DOWN));

        registerTransactionExtension(bean);
        TransactionCreateBean requestBean = new TransactionCreateBean();
        BeanUtils.copyProperties(bean, requestBean);

        requestBean.setMemberCurrentAmount(bean.getAvailableBalance());
        requestBean.setMemberCurrentActualAmount(bean.getActualBalance());
        boolean result = transactionServiceGrpc.createTransaction(requestBean);
        if(result){
            registerTransactionDetail(bean);
            if(isLocalBankTransfer()) {
                registerLocalBankTransferLog(bean);
            }
            if(isCryptocurrencyToExchange()){
                registerCryptocurrencyLog(bean);
            }
            if(isRegisterTransactionPromo()) {
                registerTransactionPromo(bean);
            }
        }
    }

    protected boolean isLocalBankTransfer() {
        return false;
    }

    protected boolean isCryptocurrencyToExchange() {
        return false;
    }

    protected boolean isRegisterTransactionPromo(){
        return false;
    }

    private void registerCryptocurrencyLog(T bean) {
        TransactionCryptocurrencyCreateBean transactionCryptoBean = new TransactionCryptocurrencyCreateBean();
        BeanUtils.copyProperties(bean.getCryptocurrencyInfoBean(), transactionCryptoBean);
        transactionCryptoBean.setTransactionId(bean.getTransactionId());
        transactionCryptoBean.setModifiedDate(bean.getModifiedDate());
        transactionCryptoBean.setModifiedBy(bean.getModifiedBy());

        boolean result = transactionServiceGrpc.createTransactionCryptocurrency(transactionCryptoBean);
        logger.info("[{}]createTransactionCryptocurrency result:{}", bean.getTransactionId(), result);
    }

    private void registerTransactionPromo(T bean) {
        List<String> promoCodeList = bean.getPromoCodeList();
        String transactionId = bean.getTransactionId();
        String modifiedBy = bean.getModifiedBy();
        doRegisterTransactionPromo(promoCodeList, transactionId, modifiedBy);
    }

    private void doRegisterTransactionPromo(List<String> promoCodeList, String transactionId, String modifiedBy) {
        if(promoCodeList == null) return;
        for (String promoCode : promoCodeList) {
            CreateTransactionPromotionBean createBean = new CreateTransactionPromotionBean(transactionId, promoCode);
            createBean.setModifiedBy(modifiedBy);
            try {
                transactionService.createTransactionPromotion(createBean);
            }catch(Exception e){
                logger.error("[{}]createTransactionPromotion fail, promoCode:{}", transactionId, promoCode, e);
            }
        }
    }

    protected void registerTransactionExtension(T bean){}

    private void registerLocalBankTransferLog(T bean) {
        // create acct_trans_localbank data to log bank details
        TransactionLocalBank transactionLocalBank = new TransactionLocalBank();
        transactionLocalBank.setTransactionId(bean.getTransactionId());
        BeanUtils.copyProperties(bean.getLocalBankTransfer(), transactionLocalBank);
        transactionLocalBank.setModifiedBy(bean.getModifiedBy());
        transactionLocalBank.setModifiedDate(new Date());

        boolean result = transactionServiceGrpc.createTransactionLocalBank(transactionLocalBank);
        logger.info("[{}]createTransactionLocalBank result:{}", bean.getTransactionId(), result);
    }

    private void registerTransactionDetail(T bean){
        TransactionDetailUpdateBean transactionDetailUpdateBean = initOtherTransactionDetail(bean);
        if(bean.getExpiredTimeSeconds() != null){
            if(transactionDetailUpdateBean == null) {
                transactionDetailUpdateBean = new TransactionDetailUpdateBean();
            }
            Date expiredDate = DateUtils.addSeconds(bean.getTransactionDate(), bean.getExpiredTimeSeconds());
            bean.setExpiredDate(expiredDate);
            transactionDetailUpdateBean.setExpiredDate(expiredDate);
        }
        if(transactionDetailUpdateBean != null) {
            transactionDetailUpdateBean.setTransactionId(bean.getTransactionId());
            transactionDetailUpdateBean.setModifiedBy(bean.getModifiedBy());
            transactionDetailUpdateBean.setModifiedDate(new Date());
            transactionDetailServiceGrpc.updateTransactionDetail(transactionDetailUpdateBean);
        }
    }

    protected TransactionDetailUpdateBean initOtherTransactionDetail(T bean){
        return null;
    }

    abstract void beanValidation(T bean);

    protected void doBeanValidation(T bean) {
        try {
            beanValidator.beanValid(bean);
        }catch(PaymentException e){
            logger.error("paymentProcess.beanValidation fail.", e);
            throw e;
        }
    }

    protected TransactionBean searchTransactionByTransId(String transactionId){
        TransactionBean transactionBean = transactionServiceGrpc.searchTransactionByTransId(transactionId);
        if(transactionBean == null){
            throw new PaymentException(ResponseStatus.Transaction_NotFound, transactionId);
        }
        return transactionBean;
    }

    private void validWithdrawalBalance(BigDecimal requestAmount, BigDecimal currentBalance, String memberCode){
        // check member balance is enough: request amount > availableBalance/rebateBalance then throw exception
        if(BigDecimalUtils.gt(requestAmount, currentBalance)){
            logger.error("[{}]Member_Insufficient_Balance for withdrawal, available balance:{} request amount:{}",
                    memberCode, currentBalance, requestAmount);
            throw new PaymentGeneralException(ResponseStatus.Member_Insufficient_Balance);
        }
    }
}
