package com.gateways.eternal;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.service.RestTemplateService;
import com.tool.MD5Utils;
import com.tools.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class Eternal {

    @Autowired
    RestTemplateService restTemplateService;

    public void depositProcess(EternalDepositRequestBean requestBean){
        EternalConfig config = new EternalConfig();
        config.setMerchantId("2566582");
        config.setDepositUrl("https://api-bankpay-manager.link6.co/gatewayOrder?time=%s&sign=%s");
        config.setSecureKey("76fe56ccad99fa4115a8d61a6c3f0c56fc76");

        long currentTimeMillis = System.currentTimeMillis() / 1000;

        requestBean.setMerchantId(config.getMerchantId());
        requestBean.setTransFlowNumber(requestBean.getOrderId());
//        requestBean.getInfo().setUserId("转账AAAC");

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> params = objectMapper.convertValue(requestBean, new TypeReference<Map<String, Object>>(){});
        String jsonBody = JsonUtils.toJson(params);
        String md5 = MD5Utils.getMD5(jsonBody + currentTimeMillis + "fc76");

        String depositUrl = String.format(config.getDepositUrl(), currentTimeMillis, md5);

        log.info(jsonBody);
        log.info("{}", params);
        String returnMsg = restTemplateService.postBody(depositUrl, jsonBody);
        log.info("return msg:{}", returnMsg);
    }
}
