package com.bean;

import dao.enums.Action;
import dao.enums.Reason;
import dao.enums.TransactionStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Accessors(chain = true)
@Data
public class ChangeTransactionStatusBean {

    @NotBlank
    String transactionId;
    @NotNull
    TransactionStatus newTransactionStatus;
    @NotNull
    Action action;
//    @NotNull
    Reason reason;
    String remark;
    String modifiedBy;

    BigDecimal currentBalance;
    BigDecimal currentActualBalance;
    String merchantBankAccountCode;
    BigDecimal changeAmount;

    public ChangeTransactionStatusBean() {
    }

    public ChangeTransactionStatusBean(String transactionId, String modifiedBy, Reason reason, String remark) {
        this.transactionId = transactionId;
        this.modifiedBy = modifiedBy;
        this.reason = reason;
        this.remark = remark;
    }
}
