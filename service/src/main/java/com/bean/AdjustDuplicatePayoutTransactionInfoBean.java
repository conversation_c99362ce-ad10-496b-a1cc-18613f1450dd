package com.bean;

import dao.enums.Category;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdjustDuplicatePayoutTransactionInfoBean extends TransactionInfoBean{

    public AdjustDuplicatePayoutTransactionInfoBean(String currencyCode, Integer memberId, BigDecimal amount) {
        super(currencyCode, memberId, amount);
    }
    @NotNull
    private String pgMerchantCode;
    private Integer pgPaymentTypeId;
    private String pgReplaceTransId;
    private String originalTransId;

    @Override
    @NotNull
    public String getModifiedBy() {
        return super.getModifiedBy();
    }

    @Override
    @NotNull
    public Category getCategoryId() {
        return super.getCategoryId();
    }
}
