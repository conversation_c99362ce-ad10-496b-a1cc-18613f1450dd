package com.service.impl;

import com.bean.DepositMerchantBankAccount;
import com.exception.PaymentException;
import com.grpcclient.merchantBankAccount.MerchantBankAccountServiceGrpc;
import com.service.BaseTest;
import com.service.MerchantBankAccountService;
import dao.bean.BankAccountType;
import dao.bean.DepositMerchantBankSummaryAmount;
import dao.bean.MerchantBankAccount;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class MerchantBankAccountServiceImplTest_mock extends BaseTest {

    @Autowired
    @InjectMocks
    MerchantBankAccountService merchantBankAccountService;

    @Mock
    MerchantBankAccountServiceGrpc merchantBankAccountServiceGrpc;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @DataProvider
    private final Object[][] personal3AndEnterprise2() {

        List<MerchantBankAccount> mockReturnList = Arrays.asList(
                MerchantBankAccount.builder().code("Personal1").bankAccountType(BankAccountType.Personal).build(),
                MerchantBankAccount.builder().code("Personal2").bankAccountType(BankAccountType.Personal).build(),
                MerchantBankAccount.builder().code("Personal3").bankAccountType(BankAccountType.Personal).build(),
                MerchantBankAccount.builder().code("Enterprise1").bankAccountType(BankAccountType.Enterprise).build(),
                MerchantBankAccount.builder().code("Enterprise2").bankAccountType(BankAccountType.Enterprise).build()
                );
        return new Object [][] {
                {mockReturnList}
        };
    }

    @Test(dataProvider = "personal3AndEnterprise2")
    public void testGetDepositMerchantBankAccountList_lessthan10000(List<MerchantBankAccount> mockReturnList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("9999");

        // expected value
        List<String> expectList = Arrays.asList("Personal1", "Personal2", "Personal3");

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockReturnList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectList.contains(merchantBankAccount.getCode()));
        }
    }

    @Test(dataProvider = "personal3AndEnterprise2")
    public void testGetDepositMerchantBankAccountList_morethan10000(List<MerchantBankAccount> mockReturnList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        List<String> expectList = Arrays.asList("Enterprise1", "Enterprise2");

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockReturnList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectList.contains(merchantBankAccount.getCode()));
        }
    }

    @Test(expectedExceptions = PaymentException.class, expectedExceptionsMessageRegExp = ".*Merchant bank account not found.*")
    public void testGetDepositMerchantBankAccountList_noMerchantBankAccount() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        List<MerchantBankAccount> mockList = Mockito.mock(List.class);
        when(mockList.size()).thenReturn(0);
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
    }

    @Test
    public void testGetDepositMerchantBankAccountList_onePersonalAccount() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        String expectedValue = "testPersonal1";

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testPersonal1").bankAccountType(BankAccountType.Personal).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertEquals(merchantBankAccount.getCode(), expectedValue);
        }
    }

    @Test
    public void testGetDepositMerchantBankAccountList_oneEnterpriseAccount() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        String expectedValue = "testEnterprise1";

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertEquals(merchantBankAccount.getCode(), expectedValue);
        }
    }

    @DataProvider
    private final Object[][] personal1AndEnterprise1() {

        List<MerchantBankAccount> mockReturnList = Arrays.asList(
                MerchantBankAccount.builder().code("Personal1").bankAccountType(BankAccountType.Personal).build(),
                MerchantBankAccount.builder().code("Enterprise1").bankAccountType(BankAccountType.Enterprise).build()
        );
        return new Object [][] {
                {mockReturnList}
        };
    }

    @Test(dataProvider = "personal1AndEnterprise1")
    public void testGetDepositMerchantBankAccountList_onePersonalAccount_oneEnterpriseAccount_morethan10000(List<MerchantBankAccount> mockList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        String expectedValue = "Enterprise1";

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertEquals(merchantBankAccount.getCode(), expectedValue);
        }
    }

    @Test(dataProvider = "personal1AndEnterprise1")
    public void testGetDepositMerchantBankAccountList_onePersonalAccount_oneEnterpriseAccount_lessthan10000(List<MerchantBankAccount> mockList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("100");

        // expected value
        String expectedValue = "Personal1";

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertEquals(merchantBankAccount.getCode(), expectedValue);
        }
    }

    @Test
    public void testGetDepositMerchantBankAccountList_twoEnterpriseAccount_lessthan10000() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("100");

        // expected value
        List expectedValue = Arrays.asList("testEnterprise1", "testEnterprise2");

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise).build());
        mockList.add(MerchantBankAccount.builder().code("testEnterprise2").bankAccountType(BankAccountType.Enterprise).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectedValue.contains(merchantBankAccount.getCode()));
        }
    }

    @Test
    public void testGetDepositMerchantBankAccountList_morethan10000_enterpriseListIsFull() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        List expectedValue = Arrays.asList("testPersonal1");

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise).maxDailyLimitAmount(new BigDecimal("200000")).limitStatus(true).build());
        mockList.add(MerchantBankAccount.builder().code("testPersonal1").bankAccountType(BankAccountType.Personal).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Arrays.asList(
                new DepositMerchantBankSummaryAmount("testEnterprise1", new BigDecimal("190001"))
        );

        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectedValue.contains(merchantBankAccount.getCode()));
        }
    }

    @Test
    public void testGetDepositMerchantBankAccountList_morethan10000_enterprise1IsFull() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        List expectedValue = Arrays.asList("testEnterprise2");

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise).maxDailyLimitAmount(new BigDecimal("200000")).limitStatus(true).build());
        mockList.add(MerchantBankAccount.builder().code("testEnterprise2").bankAccountType(BankAccountType.Enterprise).maxDailyLimitAmount(new BigDecimal("200000")).limitStatus(true).build());
        mockList.add(MerchantBankAccount.builder().code("testPersonal1").bankAccountType(BankAccountType.Personal).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Arrays.asList(
                new DepositMerchantBankSummaryAmount("testEnterprise1", new BigDecimal("190001"))
        );

        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectedValue.contains(merchantBankAccount.getCode()));
        }
    }

    @Test
    public void testGetDepositMerchantBankAccountList_threePersonalAccount_oneEnterpriseAccount_morethan10000() {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("10000");

        // expected value
        List expectedValue = Arrays.asList("testPersonal1", "testPersonal2", "testPersonal3");

        List<MerchantBankAccount> mockList = new ArrayList<>();
        mockList.add(MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise)
                .maxDailyLimitAmount(new BigDecimal("200000")).minLimitAmount(new BigDecimal("20000")).limitStatus(true).build());
        mockList.add(MerchantBankAccount.builder().code("testPersonal1").bankAccountType(BankAccountType.Personal).build());
        mockList.add(MerchantBankAccount.builder().code("testPersonal2").bankAccountType(BankAccountType.Personal).build());
        mockList.add(MerchantBankAccount.builder().code("testPersonal3").bankAccountType(BankAccountType.Personal).build());
        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Arrays.asList(

        );

        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectedValue.contains(merchantBankAccount.getCode()));
        }
    }

    @DataProvider
    private final Object[][] haveMaxLimitMerchantBankAccount() {

        List<MerchantBankAccount> mockReturnList = new ArrayList<>(Arrays.asList(
                MerchantBankAccount.builder().code("testEnterprise1").bankAccountType(BankAccountType.Enterprise)
                        .maxDailyLimitAmount(new BigDecimal("200000")).minLimitAmount(new BigDecimal("20000")).limitStatus(true).build(),
                MerchantBankAccount.builder().code("testPersonal1").maxLimitAmount(new BigDecimal("15000")).bankAccountType(BankAccountType.Personal).limitStatus(true).build(),
                MerchantBankAccount.builder().code("testPersonal2").maxLimitAmount(new BigDecimal("10000")).bankAccountType(BankAccountType.Personal).limitStatus(true).build(),
                MerchantBankAccount.builder().code("testPersonal3").maxLimitAmount(new BigDecimal("10000")).bankAccountType(BankAccountType.Personal).limitStatus(true).build()
        ));
        return new Object [][] {
                {mockReturnList}
        };
    }

    @Test(dataProvider = "haveMaxLimitMerchantBankAccount")
    public void testGetDepositMerchantBankAccountList_lessthanMaxLimitAmount(List<MerchantBankAccount> mockReturnList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("15000");

        // expected value
        List<String> expectList = Arrays.asList("testPersonal1");

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockReturnList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectList.contains(merchantBankAccount.getCode()));
        }
    }

    @Test(dataProvider = "haveMaxLimitMerchantBankAccount",
            expectedExceptions = PaymentException.class, expectedExceptionsMessageRegExp = ".*Merchant bank account not found.*")
    public void testGetDepositMerchantBankAccountList_morethanMaxLimitAmount(List<MerchantBankAccount> mockReturnList) {
        int brandId = 1;
        BigDecimal amount = new BigDecimal("15001");

        // expected value
        List<String> expectList = Arrays.asList("testPersonal1");

        when(merchantBankAccountServiceGrpc.getMerchantBankAccountByFilter(any())).thenReturn(mockReturnList);

        List<DepositMerchantBankSummaryAmount> todayDepositAmountList = Collections.emptyList();
        List<DepositMerchantBankAccount> merchantBankAccountList = merchantBankAccountService.getDepositMerchantBankAccountList(brandId, amount, todayDepositAmountList);
        logJsonResult(merchantBankAccountList);
        Assert.assertTrue(merchantBankAccountList.size()>0);
        for (MerchantBankAccount merchantBankAccount : merchantBankAccountList) {
            Assert.assertTrue(expectList.contains(merchantBankAccount.getCode()));
        }
    }
}