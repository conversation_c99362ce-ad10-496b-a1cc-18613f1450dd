package com.service.process.cryptocurreny;

import com.service.BaseTest;
import com.service.bean.CryptocurrencyExchangeWithdrawalResponse;
import com.service.enums.BitfinexDepositMethod;
import com.service.process.cryptocurreny.bean.CryptocurrencyExchangeWithdrawalProcessBean;
import com.service.process.cryptocurreny.impl.BitfinexWithdrawalProcess;
import com.service.process.cryptocurreny.impl.FtxWithdrawalProcess;
import dao.bean.MerchantCryptocurrencyAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.math.BigDecimal;

public class CryptocurrencyExchangeWithdrawalProcessTest extends BaseTest {

    @Autowired
    BitfinexWithdrawalProcess bitfinexWithdrawalProcess;

    @Autowired
    FtxWithdrawalProcess ftxWithdrawalProcess;

    @Test
    public void testBitfinexWithdrawalProcess() {
        MerchantCryptocurrencyAccount account = MerchantCryptocurrencyAccount.builder()
                .id(1)
                .code("test1")
                .build();

        CryptocurrencyExchangeWithdrawalProcessBean bean = CryptocurrencyExchangeWithdrawalProcessBean.builder()
                .withdrawalAmount(new BigDecimal("1"))
                .address("0x123")
                .method(BitfinexDepositMethod.TETHERUSE)
                .merchantTransId("Test_"+ getRandomDateTimeSequence())
                .merchantAccount(account)
                .build();
        CryptocurrencyExchangeWithdrawalResponse result = bitfinexWithdrawalProcess.withdrawalProcess(bean);
        logJsonResult(result);
    }

    @Test
    public void testFtxWithdrawalProcess() {
        MerchantCryptocurrencyAccount account = MerchantCryptocurrencyAccount.builder()
                .id(1)
                .code("test1")
                .build();

        CryptocurrencyExchangeWithdrawalProcessBean bean = CryptocurrencyExchangeWithdrawalProcessBean.builder()
                .withdrawalAmount(new BigDecimal("1"))
                .address("0x123")
                .method(BitfinexDepositMethod.TETHERUSE)
                .merchantTransId("Test_"+ getRandomDateTimeSequence())
                .merchantAccount(account)
                .build();
        CryptocurrencyExchangeWithdrawalResponse result = ftxWithdrawalProcess.withdrawalProcess(bean);
        logJsonResult(result);
    }
}