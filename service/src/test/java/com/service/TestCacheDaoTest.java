package com.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

public class TestCacheDaoTest extends BaseTest{

    @Autowired
    TestCacheDao testCacheDao;

    @Test
    public void testSave() throws InterruptedException {
        String typeId1 = "type111";
        String typeId2 = "type222";
        // 模拟第一次查询
        String returnStr1 = testCacheDao.select(typeId1);
        logger.info("模拟第1次查询:{}", returnStr1);
        // 模拟第二次查询
        String returnStr2 = testCacheDao.select(typeId1);
        logger.info("模拟第2次查询:{}", returnStr2);
        // 模拟第3次查询
        logger.info("sleep 2 sec............");
        Thread.sleep(2000L);
        String returnStr3 = testCacheDao.select(typeId1);
        logger.info("模拟第3次查询:{}", returnStr3);
        // 模拟第4次查询
        testCacheDao.delete(typeId1);
        String returnStr4 = testCacheDao.select(typeId1);
        logger.info("模拟第4次查询:{}", returnStr4);
        // 模拟第5次查询
        String returnStr5 = testCacheDao.select(typeId2);
        logger.info("模拟第5次查询:{}", returnStr5);
    }

}