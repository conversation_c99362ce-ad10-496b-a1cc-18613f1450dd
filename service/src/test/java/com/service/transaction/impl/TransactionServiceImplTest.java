package com.service.transaction.impl;

import com.bean.AgentTransactionFilterBean;
import com.bean.DepositConfirmInputBean;
import com.exception.PaymentException;
import com.service.BaseTest;
import com.service.bean.*;
import com.service.transaction.TransactionService;
import dao.bean.*;
import dao.entity.AcctTransReferral;
import dao.enums.TransactionType;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TransactionServiceImplTest extends BaseTest {

    @Autowired
    TransactionService transactionService;

    @Test
    public void testSearchTransactionByAgent() {
        AgentTransactionFilterBean filterBean = new AgentTransactionFilterBean();
        filterBean.setAgentId(1);
        filterBean.setTransactionType(TransactionType.Snatch);
//        filterBean.setTransactionStatus(TransactionStatus.Approved);
        PaginationResultList<TransactionBean> resultList = transactionService.searchTransactionByAgent(filterBean);
        logger.info("Result:{}", getJsonStringResponse(resultList));
    }

    @Test(expectedExceptions = PaymentException.class, expectedExceptionsMessageRegExp = ".*Invalid data.*")
    public void testSearchTransactionByAgent_noAgentId() {
        AgentTransactionFilterBean filterBean = new AgentTransactionFilterBean();
        PaginationResultList<TransactionBean> resultList = transactionService.searchTransactionByAgent(filterBean);
        logger.info("Result:{}", getJsonStringResponse(resultList));
    }

    @Test
    public void testSearchAdjustTransactionByAgent() {
        AgentTransactionFilterBean filterBean = new AgentTransactionFilterBean();
        filterBean.setAgentId(1);
        PaginationResultList<TransactionBean> resultList = transactionService.searchAdjustTransactionByAgent(filterBean);
        logger.info("Result:{}", getJsonStringResponse(resultList));
    }

    @Test
    public void testGetSequenceNumber() {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 100; i++) {
            executor.submit(() -> {
                String sequenceNumber = transactionService.getSequenceNumber(3);
                logger.info("sequenceNumber:{}", sequenceNumber);
            });
        }
        executor.shutdown();

    }

    @Test
    public void testGetSnatchPendingTransaction() {
        Integer brandId = 1;
        Date endDate = new Date();
        endDate = DateUtils.addMinutes(endDate, -30);
        Integer dataSize = 100;
        Date fromDate = null;
        PaginationResultList<TransactionBean> result = transactionService.getSnatchPendingTransaction(brandId, fromDate, endDate, dataSize, true);
        logger.info("Result:{}", result.getTotalResults());
        List<TransactionBean> pendingTransactionList = result.getResultList();
        if(pendingTransactionList.size() > dataSize){
            pendingTransactionList.subList(0, dataSize-1);
        }

//        logger.info("Result transactionBeans:{}", transactionBeans.size());
        logger.info("Result transactionBeans:{}", pendingTransactionList.size());
    }

    @Test
    public void testGetDepositPendingTransaction() {
        PaginationResultList<TransactionBean> result = transactionService.getDepositPendingTransaction(100);
        logger.info("Result:{}", getJsonStringResponse(result));
    }

    @Test
    public void testGetDepositRemarkNumber() {
        for (int i = 0; i < 200; i++) {
            logger.info("Result:{}", transactionService.getDepositRemarkNumber());
        }

    }

    @Test
    public void testUpdateTransactionLocalBank() {
        String transactionId = "D1571883049294379";
        String bankAccountCode = "CCBC2598";
        String modifiedBy = "test";
        boolean result = transactionService.updateTransactionLocalBank(transactionId, bankAccountCode, modifiedBy);
        logJsonResult(result);
    }


    @Test
    public void testSearchActiveTransactionReferenceByReferenceId() {
        String referenceId = "******************";
        List<TransactionReferenceBean> result = transactionService.searchActiveTransactionReferenceByReferenceId(referenceId);
        logJsonResult(result);
    }

    @Test
    public void testCreateTransactionPromotion() {
        String transactionId = "st"+System.currentTimeMillis();
        String promoCode = "stpromo1";
        CreateTransactionPromotionBean createBean = new CreateTransactionPromotionBean(transactionId, promoCode);
        createBean.setModifiedBy("service test");
        boolean result = transactionService.createTransactionPromotion(createBean);
        logJsonResult(result);
    }

    @Test
    public void testAddRetryTimesForTransactionNotification() {

        Integer logId = 94;
        String transactionId = "test1";
        String remark = "test12345";
        boolean result = transactionService.addRetryTimesForTransactionNotification(logId, transactionId, remark);
        logJsonResult(result);
    }

    @Test
    public void testUpdateActiveStatusForTransactionNotification() {
        Integer logId = 94;
        String transactionId = "test1";
        boolean result = transactionService.updateActiveStatusForTransactionNotification(logId, transactionId);
        logJsonResult(result);
    }

    @Test
    public void testSearchTransactionNotificationById() {
        Integer id = 106;
        TransactionNotification result = transactionService.searchTransactionNotificationById(id);
        logJsonResult(result);
    }

    @Test
    public void testTestSearchTransactionNotificationById() {
        Integer id = 106;
        String transactionId = "SW1592202829964004";
        TransactionNotification result = transactionService.searchTransactionNotificationById(id, transactionId);
        logJsonResult(result);
    }

    @Test
    public void testWebScrapingNotify() {
        String transactionId = "D1592900606668001";
        boolean result = transactionService.webScrapingNotify(transactionId);
        logJsonResult(result);
    }

    @Test
    public void testTestGetTodayTotalDepositAmount() {
        int brandId = 1;
        List<DepositMerchantBankSummaryAmount> result = transactionService.getTodayTotalDepositAmount(brandId);
        logJsonResult(result);
    }

    @Test
    public void testGetTodayTotalDepositLocalBankAmount() {
        int brandId = 1;
        List<DepositMerchantBankSummaryAmount> result = transactionService.getTodayTotalDepositLocalBankAmount(brandId);
        logJsonResult(result);
    }

    @Test
    public void testGetTodayTotalDepositCryptocurrencyAmount() {
        int brandId = 1;
        List<DepositMerchantBankSummaryAmount> result = transactionService.getTodayTotalDepositCryptocurrencyAmount(brandId);
        logJsonResult(result);
    }

    @Test
    public void testDepositConfirm() {
        String transactionId = "DU1597833302516018";
        DepositConfirmInputBean bean = new DepositConfirmInputBean(transactionId);
        bean.setReceivedTransactionId("OO549999");
        boolean result = transactionService.depositConfirm(bean);
        logJsonResult(result);
    }

    @Test
    public void testUpdateTransactionCryptocurrencyReceivedTransId() {
        String transactionId = "DU1597832497211017";
        String receivedTransId = "xxxxxx1231234544";
        UpdateTransCryptoReceivedTransIdInputBean bean = new UpdateTransCryptoReceivedTransIdInputBean(transactionId, receivedTransId);
        bean.setModifiedBy("testcase");
        boolean result = transactionService.updateTransactionCryptocurrencyReceivedTransId(bean);
        logJsonResult(result);
    }

    @Test
    public void testUpdateTransactionCryptocurrencyReceivedAmount() {

        String transactionId = "DU1597906811889003";
        BigDecimal receivedAmount = new BigDecimal("10");
        UpdateTransCryptoReceivedAmountInputBean bean = new UpdateTransCryptoReceivedAmountInputBean(transactionId, receivedAmount);
        bean.setModifiedBy("testcase");
        BigDecimal result = transactionService.updateTransactionCryptocurrencyReceivedAmount(bean);
        logJsonResult(result);
    }

    @Test
    public void testGetDepositCryptocurrencyConfirmedTransaction() {
        Integer dataSize = null;
        Date startDate = Date.from(LocalDateTime.now().minusHours(1).atZone(ZoneId.systemDefault()).toInstant());
        PaginationResultList<TransactionBean> result = transactionService.getDepositCryptocurrencyConfirmedTransaction(dataSize, startDate);
        logJsonResult(result);
    }

    @Test
    public void testUpdateReceivedTransactionId() {
        String transactionId = "DU1599624405681005";
        String receivedTransactionId = "223344";
        String modifiedBy = "testcase";
        UpdateReceivedTransactionIdBean bean = UpdateReceivedTransactionIdBean.builder()
                .transactionId(transactionId)
                .receivedTransactionId(receivedTransactionId)
                .modifiedBy(modifiedBy)
                .build();
        boolean result = transactionService.updateReceivedTransactionId(bean);
        logJsonResult(result);
    }

    @Test
    public void testSearchMemberLastDepositCryptocurrencyTransaction() {
        Integer memberId = 70021;
        Date dateFrom = Date.from(LocalDate.of(2020,9,17).atStartOfDay(ZoneId.systemDefault()).toInstant());
        String walletCode = "Sam";
        TransactionBean result = transactionService.searchMemberLastDepositCryptocurrencyTransaction(memberId, dateFrom, walletCode);
        logJsonResult(result);
    }

    @Test
    public void testUpdateTransactionMemberReference() {
        UpdateTransactionMemberReferenceInputBean bean = UpdateTransactionMemberReferenceInputBean.builder()
                .transactionId("DU1600424230257001")
                .memberReference("Sam")
                .modifiedBy("testcase")
                .build();
        boolean result = transactionService.updateTransactionMemberReference(bean);
        logJsonResult(result);
    }


    @Test
    public void testRollbackTransactionRebate() {
        String transactionId = "SA2B1603783313238029";
        RollbackTransactionRebateBean result = transactionService.rollbackTransactionRebate(transactionId, null, true);
        logJsonResult(result);
    }

    @Test
    public void testGetNotRejectDepositLocalBankTransferTransactionByReferenceId() {
        String referenceId = "2";
        Date startDate = Date.from(LocalDate.of(2020,12,2).atStartOfDay(ZoneId.systemDefault()).toInstant());
        List<TransactionBean> result = transactionService.getNotRejectDepositLocalBankTransferTransactionByReferenceId(referenceId, startDate);
        logJsonResult(result);
    }

    @Test
    public void testRegisterDepositMapping() {
        String transactionId = "DU1609902391386001";
        boolean result = transactionService.registerDepositMapping(transactionId);
        logJsonResult(result);
    }

    @Test
    public void testGetActiveTransactionReferral() {
        String transactionId = "test123";
        List<AcctTransReferral> result = transactionService.getActiveTransactionReferral(transactionId);
        logJsonResult(result);
    }

    @Test
    public void testUpdateReceivedUtrTransactionDetail() {
        String transactionId = "SLBTU1719481672374003";
        String receivedUtr = "123";
        String modifiedBy = "testcase";
        boolean result = transactionService.updateReceivedUtrTransactionDetail(transactionId, receivedUtr, modifiedBy, false);
        logJsonResult(result);
    }
}