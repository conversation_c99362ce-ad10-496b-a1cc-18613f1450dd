package com.service.transaction.impl;

import com.bean.Redirector;
import com.bean.TransactionDetails;
import com.bean.WithdrawalCryptocurrencyTransactionInfoBean;
import com.grpcclient.bean.MemberCurrentBalance;
import com.grpcclient.member.MemberCurrencyCode;
import com.service.BaseTest;
import com.service.MemberService;
import com.service.MemberWalletService;
import com.service.bean.MemberDetail;
import com.service.transaction.CreateTransactionService;
import com.service.transaction.UpdateTransactionStatusProcess;
import com.service.transaction.process.WithdrawalCryptocurrencyPaymentProcess;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.math.BigDecimal;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class CreateTransactionServiceImplTest_WithdrawalCryptocurrency_mockMember extends BaseTest {

    @Autowired
    CreateTransactionService createTransactionService;

    @InjectMocks
    @Autowired
    WithdrawalCryptocurrencyPaymentProcess withdrawalCryptocurrencyPaymentProcess;

    @Mock
    @Autowired
    private MemberService memberService;

    @Autowired
    @InjectMocks
    UpdateTransactionStatusProcess updateTransactionStatusProcess;

    @Mock
    @Autowired
    MemberWalletService memberWalletService;

    @BeforeMethod
    public void setup() {
        MockitoAnnotations.initMocks(this);

        MemberDetail memberDetail = MemberDetail.builder()
                .brandID(1)
                .memberCode("<EMAIL>")
                .memberID(1001)
                .agentID(1)
                .actualBalance(new BigDecimal("100"))
                .availableBalance(new BigDecimal("100"))
                .actualUsdtBalance(new BigDecimal("200"))
                .availableUsdtBalance(new BigDecimal("200"))
                .ip("*******")
                .deviceName("device_name")
                .name("member_name")
                .walletAddress("USDT_WALLET_ADDRESS")
                .holdWithdrawal(false)
                .build();
        when(memberService.getMemberDetailByMemberId(anyInt())).thenReturn(memberDetail);

        MemberCurrentBalance updateBalanceResult = new MemberCurrentBalance();
        updateBalanceResult.setActualBalance(new BigDecimal("10"));
        updateBalanceResult.setAvailableBalance(new BigDecimal("20"));
        updateBalanceResult.setRebateAmount(new BigDecimal("30"));
        when(memberWalletService.adjustMemberBalance(anyInt(), any(), any(), anyString(), anyString(), any(), any())).thenReturn(updateBalanceResult);

        when(memberWalletService.deductMemberBalance(anyInt(), any(), any(), anyString(), anyString(), MemberCurrencyCode.CNY)).thenReturn(updateBalanceResult);
    }
    
    @Test
    public void testCreateWithdrawalCryptocurrency() {
        WithdrawalCryptocurrencyTransactionInfoBean bean = new WithdrawalCryptocurrencyTransactionInfoBean();
        bean.setMemberId(1);
        bean.setCurrencyCode("USDT");
        bean.setAmount(new BigDecimal("100"));
        bean.setTargetWalletAddress("walletAddress11");

        Redirector<TransactionDetails> result = createTransactionService.createWithdrawalCryptocurrency(bean);
        logJsonResult(result);
    }
}