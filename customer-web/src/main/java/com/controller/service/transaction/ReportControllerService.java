package com.controller.service.transaction;

import com.controller.bean.CnyTransactionFlowListResp;
import dao.enums.Category;
import dao.enums.TransactionType;
import org.springframework.stereotype.Service;

import java.util.EnumSet;

@Service
public class ReportControllerService {

    public CnyTransactionFlowListResp.ReportTransactionType getCnyTransactionFlowType(TransactionType transactionType) {
        // Rebate Promotion Referral SystemPromotion return Reward
        if (EnumSet.of(TransactionType.Rebate, TransactionType.Promotion, TransactionType.Referral, TransactionType.SystemPromotion).contains(transactionType)) {
            return CnyTransactionFlowListResp.ReportTransactionType.Reward;
        }

        // Exchange return Exchange
        if (transactionType == TransactionType.ExchangeCurrency) {
            return CnyTransactionFlowListResp.ReportTransactionType.Exchange;
        }

        // Transfer return Transfer
        if (transactionType == TransactionType.Transfer) {
            return CnyTransactionFlowListResp.ReportTransactionType.Transfer;
        }

        // Snatch return Sell
        if (transactionType == TransactionType.Snatch) {
            return CnyTransactionFlowListResp.ReportTransactionType.Sell;
        }

        // Deposit return Buy
        if (transactionType == TransactionType.Deposit) {
            return CnyTransactionFlowListResp.ReportTransactionType.Buy;
        }

        return null;
    }

    public boolean isCnyTransactionFlowAmountNegative(TransactionType transactionType, Category category) {
        // Snatch or AdjustSnatch or AdjustSnatchDuplicatePay return true
        if (EnumSet.of(TransactionType.Snatch, TransactionType.AdjustSnatch, TransactionType.AdjustSnatchDuplicatePay).contains(transactionType)) {
            return true;
        }
        // ExchangeCurrency + CategoryId=18(EXCHANGE_CURRENCY_USDT_CNY) return true
        if (transactionType == TransactionType.ExchangeCurrency && category == Category.EXCHANGE_CURRENCY_USDT_CNY) {
            return true;
        }
        // Transfer + CategoryId=21(TRANSFER_OUT) return true
        if (transactionType == TransactionType.Transfer && category == Category.TRANSFER_OUT) {
            return true;
        }

        return false;
    }
}