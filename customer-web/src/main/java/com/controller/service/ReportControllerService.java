package com.controller.service;
public class ReportControllerService{
public ReportControllerService() {
}
public com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType getCnyTransactionFlowType(dao.enums.TransactionType transactionType) {
        // Rebate Promotion Referral SystemPromotion return Reward
        if(java.util.EnumSet.of(dao.enums.TransactionType.Rebate, dao.enums.TransactionType.Promotion, dao.enums.TransactionType.Referral, dao.enums.TransactionType.SystemPromotion).contains(transactionType)){
            return com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType.Reward;
        }

        // Exchange return Exchange
        if(transactionType == dao.enums.TransactionType.ExchangeCurrency){
            return com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType.Exchange;
        }

        // Transfer return Transfer
        if(transactionType == dao.enums.TransactionType.Transfer){
            return com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType.Transfer;
        }

        // Snatch return Sell
        if(transactionType == dao.enums.TransactionType.Snatch){
            return com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType.Sell;
        }

        // Deposit return Buy
        if(transactionType == dao.enums.TransactionType.Deposit){
            return com.controller.bean.CnyTransactionFlowListResp.ReportTransactionType.Buy;
        }

        return null;
    }private boolean isCnyTransactionFlowAmountNegative(dao.enums.TransactionType transactionType, dao.enums.Category category) {
        // Snatch or AdjustSnatch or AdjustSnatchDuplicatePay return true
        if(java.util.EnumSet.of(dao.enums.TransactionType.Snatch, dao.enums.TransactionType.AdjustSnatch, dao.enums.TransactionType.AdjustSnatchDuplicatePay).contains(transactionType)){
            return true;
        }
        // ExchangeCurrency + CategoryId=18(EXCHANGE_CURRENCY_USDT_CNY) return true
        if(transactionType == dao.enums.TransactionType.ExchangeCurrency && category == dao.enums.Category.EXCHANGE_CURRENCY_USDT_CNY){
            return true;
        }
        // Transfer + CategoryId=21(TRANSFER_OUT) return true
        if(transactionType == dao.enums.TransactionType.Transfer && category == dao.enums.Category.TRANSFER_OUT){
            return true;
        }

        return false;
    }}