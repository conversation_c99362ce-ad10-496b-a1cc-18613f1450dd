package com.controller.others;

import com.bean.CreateMemberBankAccountBean;
import com.bean.MemberBankAccountFilterBean;
import com.bean.UpdateMemberBankAccountBean;
import com.controller.BaseController;
import com.controller.bean.AgentBankAccountResponse;
import com.controller.bean.CreateAgentBankAccountFormBean;
import com.controller.bean.ResponseVo;
import com.service.MemberBankAccountService;
import dao.bean.MemberBankAccount;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.ArrayList;
import java.util.List;

import static com.controller.constant.PaymentUrlConstant.*;
import static com.controller.constant.SwaggerApiTag.*;

@Api(tags = {MEMBER_BANK_ACCOUNT_MGMT, MEMBER_BANK_ACCOUNT})
@RestController
public class AgentBankAccountController extends BaseController {

    @Autowired
    MemberBankAccountService memberBankAccountService;

    @ApiOperation(value = "list agent bank account list", tags = {AGENT_API})
    @GetMapping(AGENT_BANK_ACCOUNT_LIST)
    public ResponseVo<List<AgentBankAccountResponse>> agentBankAccountList(
            @RequestParam @Min(1) Integer agentId,
            @RequestParam(required = false) Integer bankAccountId
            ){
        MemberBankAccountFilterBean filterBean = new MemberBankAccountFilterBean();
        filterBean.setAgentId(agentId);
        filterBean.setBankAccountId(bankAccountId);
        List<MemberBankAccount> memberBankAccounts = memberBankAccountService.searchActiveAgentBankAccount(filterBean);
        List<AgentBankAccountResponse> result = new ArrayList<>();
        AgentBankAccountResponse agentBankAccountResponse;
        for (MemberBankAccount memberBankAccount : memberBankAccounts) {
            agentBankAccountResponse = new AgentBankAccountResponse();
            BeanUtils.copyProperties(memberBankAccount, agentBankAccountResponse);
            agentBankAccountResponse.setId(memberBankAccount.getMemberBankAccountId());
            agentBankAccountResponse.setMemberName(memberBankAccount.getBankAccountName());
            agentBankAccountResponse.setBankName(memberBankAccount.getBank().getName());
            agentBankAccountResponse.setAgentId(memberBankAccount.getMemberId());
            agentBankAccountResponse.setAgentCode(memberBankAccount.getMemberCode());
            result.add(agentBankAccountResponse);
        }
        return getSuccessResponse(result);
    }

    @ApiOperation(value = "create agent bank account", tags = {AGENT_API})
    @PostMapping(AGENT_BANK_ACCOUNT_CREATE)
    public ResponseVo<Boolean> createAgentBankAccount(@Valid @RequestBody CreateAgentBankAccountFormBean formBean){
        CreateMemberBankAccountBean bean = new CreateMemberBankAccountBean();
        BeanUtils.copyProperties(formBean, bean);
        bean.setMemberId(formBean.getAgentId());
        bean.setMemberCode(formBean.getAgentCode());
        bean.setBankAccountName(formBean.getMemberBankAccountName());
        return getSuccessResponse(memberBankAccountService.createAgentBankAccount(bean));
    }

    @ApiOperation(value = "update agent bank account", tags = {AGENT_API})
    @PostMapping(AGENT_BANK_ACCOUNT_UPDATE)
    public ResponseVo<Boolean> updateAgentBankAccount(@PathVariable Integer bankAccountId, @Valid  @RequestBody CreateAgentBankAccountFormBean formBean){
        UpdateMemberBankAccountBean updateMemberBankAccountBean = new UpdateMemberBankAccountBean(bankAccountId);
        BeanUtils.copyProperties(formBean, updateMemberBankAccountBean);
        updateMemberBankAccountBean.setMemberId(formBean.getAgentId());
        updateMemberBankAccountBean.setMemberCode(formBean.getAgentCode());
        updateMemberBankAccountBean.setModifiedBy(formBean.getAgentCode());
        updateMemberBankAccountBean.setBankAccountName(formBean.getMemberBankAccountName());
        boolean result = memberBankAccountService.updateBankAccount(updateMemberBankAccountBean);
        return getSuccessResponse(result);
    }

    @ApiOperation(value = "delete agent bank account", tags = {AGENT_API})
    @PostMapping(AGENT_BANK_ACCOUNT_DELETE)
    public ResponseVo<Boolean> deleteAgentBankAccount(@PathVariable Integer agentId, @PathVariable Integer bankAccountId,
        @RequestParam String modifiedBy){
        UpdateMemberBankAccountBean updateMemberBankAccountBean = new UpdateMemberBankAccountBean(bankAccountId);
        updateMemberBankAccountBean.setMemberId(agentId);
        updateMemberBankAccountBean.setModifiedBy(modifiedBy);

        return getSuccessResponse(memberBankAccountService.deleteBankAccount(updateMemberBankAccountBean));
    }
}
