package com.controller.enums;

import dao.enums.DepositUSDTProtocol;

public enum CryptocurrencyProtocol {
    ERC20(DepositUSDTProtocol.ERC20),
    //OMNI(DepositUSDTProtocol.OMNI),
    TRC20(DepositUSDTProtocol.TRC20),
    ;

    private DepositUSDTProtocol depositUSDTProtocol;

    CryptocurrencyProtocol(DepositUSDTProtocol depositUSDTProtocol) {
        this.depositUSDTProtocol = depositUSDTProtocol;
    }

    public DepositUSDTProtocol getDepositUSDTProtocol() {
        return depositUSDTProtocol;
    }
}
