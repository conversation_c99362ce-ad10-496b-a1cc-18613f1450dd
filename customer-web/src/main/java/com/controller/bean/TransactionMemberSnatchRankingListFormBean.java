package com.controller.bean;

import com.controller.constant.SwaggerNoteConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import dao.enums.MemberSnatchRankingListOrderBy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class TransactionMemberSnatchRankingListFormBean {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(position = 1, example = "2020-02-27 00:00:00")
    @NotNull
    private Date transactionDateFrom;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(position = 2, example = "2020-02-27 23:59:59")
    @NotNull
    private Date transactionDateTo;

    @ApiModelProperty(position = 3, example = "TotalAmount", notes = SwaggerNoteConstant.optionalNote)
    @JsonProperty("sortBy")
    private MemberSnatchRankingListOrderBy orderByText = MemberSnatchRankingListOrderBy.TotalAmount;

    @ApiModelProperty(position = 99, example = "20", notes = SwaggerNoteConstant.optionalNote)
    @JsonProperty("rankSize")
    private Integer recordToReturn;
}
