package com.controller.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class CryptocurrencyExchangeWalletTransferFormBean {
    @ApiModelProperty(required = true, position = 1, notes = "source from merchant accountId")
    @NotNull
    private Integer sourceAccountId;

    @ApiModelProperty(required = true, position = 2, notes = "target to merchant accountId")
    @NotNull
    private Integer targetAccountId;

    @ApiModelProperty(required = true, position = 3)
    @NotNull
    private BigDecimal requestAmount;

    @ApiModelProperty(required = true, position = 4)
    @NotBlank
    private String modifiedBy;
}
