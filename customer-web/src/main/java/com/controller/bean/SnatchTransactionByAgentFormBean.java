package com.controller.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import dao.enums.Reason;
import dao.enums.TransactionStatus;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

@Data
public class SnatchTransactionByAgentFormBean extends PaginationFormBean{
    @NotNull
    private Integer agentId;

    private String memberCode;
    private String transactionId;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    private BigDecimal amount;
    private List<Integer> amountList;

    private SnatchTransactionStatus snatchTransactionStatus;

    public enum SnatchTransactionStatus {
        Processing(TransactionStatus.NewRequest, Reason.NO_REASON),
        Confirmed(TransactionStatus.Confirmed, Reason.PaymentGatewayConfirmed),
        Approved(TransactionStatus.Approved, Reason.Approve),
        ApprovedButNotConfirm(TransactionStatus.Approved, Reason.ApproveButNotConfirm),
        Rejected(TransactionStatus.Rejected, Reason.Reject),
        RejectedAndTimeout(TransactionStatus.Rejected, Reason.TIME_OUT),
        RejectedAndAdjust(TransactionStatus.Rejected, Reason.RejectAndAdjust),
        ;

        private TransactionStatus transactionStatus;
        private Reason reason;

        SnatchTransactionStatus(TransactionStatus transactionStatus, Reason reason) {
            this.transactionStatus = transactionStatus;
            this.reason = reason;
        }

        public static SnatchTransactionStatus getEnumByParams(TransactionStatus transactionStatus, Reason reason){
            return Stream.of(SnatchTransactionStatus.values())
                    .filter(e-> e.getTransactionStatus() == transactionStatus && e.getReason() == reason)
                    .findFirst().orElse(null);
        }

        public TransactionStatus getTransactionStatus() {
            return transactionStatus;
        }

        public Reason getReason() {
            return reason;
        }
    }
}
