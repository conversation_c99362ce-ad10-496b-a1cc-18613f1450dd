package com.controller.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UpdateReceivedTransactionFormBean {
    @ApiModelProperty(value = "訂單號", position = 1, required = true)
    @NotBlank
    public String transactionId;

    @ApiModelProperty(value = "收到的交易id", position = 2, required = false)
    public String receivedTransactionId;

    @ApiModelProperty(value = "修改者", position = 3, required = true)
    @NotBlank
    public String modifiedBy;

    @ApiModelProperty(value = "錢包", position = 4, required = false)
    public String merchantAccountCode;
}
