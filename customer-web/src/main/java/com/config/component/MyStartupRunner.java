package com.config.component;

import com.tools.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@Profile({"dev", "prod"})
@Slf4j
public class MyStartupRunner implements CommandLineRunner {
    @Autowired
    Environment env;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Override
    public void run(String... args) {
//        if(activeProfile != null && activeProfile.equals("prod")){
        log.info("[env:{}]disable json pretty printer.", activeProfile);
        JsonUtils.disablePrettyPrinter();
//        }
    }
}
