package dao.mapper;

import dao.basetest.BaseTest;
import dao.bean.PaginationResultList;
import dao.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.util.List;

public class UserDemoMapperTest extends BaseTest {

    @Autowired
    UserDemoMapper demoMapper;

    @Test
    public void testList() {
        List<User> list = demoMapper.list();
        logJsonResult(list);
    }

    @Test
    public void testListPaging() {
        List<List<?>> list = demoMapper.listPaging();
        List<?> userList = list.get(0);
        Integer count = (Integer) list.get(1).get(0);
        logJsonResult(userList);
        logJsonResult(count);
        PaginationResultList<User> paginationResultList = new PaginationResultList<>((List<User>) userList, count);
        logJsonResult(paginationResultList);
    }

    @Test
    public void testListByCondition() {
        User user = new User();
//        user.setId(1017);
        user.setName("2test1");
        List<List<?>> list = demoMapper.listByCondition(user);
        List<User> userList = (List<User>) list.get(0);
        Integer count = (Integer) list.get(1).get(0);
        PaginationResultList<User> paginationResultList = new PaginationResultList<User>(userList, count);
        logJsonResult(paginationResultList);
    }

    @Test
    public void testListById() {
        int id = 1;
        User user = demoMapper.listById(id);
        logJsonResult(user);
    }
}