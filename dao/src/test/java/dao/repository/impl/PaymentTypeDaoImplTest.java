package dao.repository.impl;

import com.tools.JsonUtils;
import dao.basetest.BaseTest;
import dao.bean.PaymentType;
import dao.bean.PaymentTypeFilter;
import dao.bean.PaymentTypeLimit;
import dao.enums.Category;
import dao.enums.PaymentGatewayType;
import dao.enums.TransactionType;
import dao.repository.PaymentTypeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Ignore;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

public class PaymentTypeDaoImplTest extends BaseTest {

    @Autowired
    PaymentTypeDao paymentTypeDao;

    @Test//(enabled = false)
    public void testGetPaymentTypeByTransactionType() {
        int brandId = 1;
        TransactionType transactionType = TransactionType.Snatch;
        Category categoryId = Category.WECHAT;
        PaymentTypeFilter paymentTypeFilter = new PaymentTypeFilter();
        paymentTypeFilter.setBrandId(brandId);
        paymentTypeFilter.setTransactionType(transactionType);
        paymentTypeFilter.setCategoryId(categoryId);
        List<PaymentType> result = paymentTypeDao.getPaymentType(paymentTypeFilter);

        logger.info(JsonUtils.toJson(result));
    }

    @Test
    @Ignore
    public void testTask() throws ExecutionException, InterruptedException {
//        ExecutorService executorService = Executors.newCachedThreadPool();
        ExecutorService executorService = Executors.newFixedThreadPool(5);
//      ExecutorService executorService = Executors.newSingleThreadExecutor();
        List<Future<?>> list = new ArrayList<>();
        long start = System.currentTimeMillis();
        FutureTask<Integer> futureTask;
        for (int i = 0; i < 10; i++){
            futureTask = new FutureTask<Integer>(new Callable(){
                public String call() throws Exception {
                    logger.info("Asynchronous task");
                    testGetPaymentTypeByTransactionType();
                    return "Callable Result";
                }
            });
            Future<?> submit = executorService.submit(futureTask);
            list.add(submit);
            System.out.println("future.get() = " + futureTask.get());
            System.out.println("************* a" + i + " *************");
        }
//        for (Future<?> future : list) {
//            logger.info("::"+future.get());
//        }
//        System.out.println("isDone:"+futureTask.isDone());
        long end = System.currentTimeMillis();
        System.out.println("Cost time:"+(end-start));
        executorService.shutdown();

    }

    @Test//(enabled = false)
    public void testGetPaymentTypeLimit() {
        TransactionType transactionType = TransactionType.Withdrawal;
        PaymentGatewayType paymentGatewayType = PaymentGatewayType.Member;
        String currencyCode = "CNY";
        PaymentTypeLimit paymentTypeLimit = paymentTypeDao.getPaymentTypeLimit(transactionType, paymentGatewayType, currencyCode);

        logger.info("Result:{}", JsonUtils.toJson(paymentTypeLimit));
    }

    @Test
    public void testGetPaymentTypeLimit_agent() {
        TransactionType transactionType = TransactionType.Withdrawal;
        PaymentGatewayType paymentGatewayType = PaymentGatewayType.Agent;
        String currencyCode = "CNY";
        PaymentTypeLimit paymentTypeLimit = paymentTypeDao.getPaymentTypeLimit(transactionType, paymentGatewayType, currencyCode);

        logger.info("Result:{}", JsonUtils.toJson(paymentTypeLimit));
    }

    @Test
    public void testGetPaymentTypeByTransactionType_getAgentType() {
        PaymentTypeFilter paymentTypeFilter = new PaymentTypeFilter();
        paymentTypeFilter.setBrandId(1);
        paymentTypeFilter.setTransactionType(TransactionType.Withdrawal);
        paymentTypeFilter.setPaymentGatewayType(PaymentGatewayType.Agent);

        List<PaymentType> result = paymentTypeDao.getPaymentType(paymentTypeFilter);
        logger.info("Result:{}", getJsonStringResponse(result));

    }
}