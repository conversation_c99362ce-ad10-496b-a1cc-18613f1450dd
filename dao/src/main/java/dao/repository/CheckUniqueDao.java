package dao.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import dao.bean.CheckUniqueCreateBean;
import dao.bean.CheckUniqueUpdateBean;
import dao.entity.CheckUnique;
import dao.mapper.CheckUniqueMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;

@Repository
public class CheckUniqueDao {
    @Autowired
    CheckUniqueMapper checkUniqueMapper;

    public Integer getCheckUniqueId(CheckUniqueCreateBean checkUniqueCreateBean){
        QueryWrapper<CheckUnique> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CheckUnique::getType, checkUniqueCreateBean.getType())
                .eq(CheckUnique::getValue, checkUniqueCreateBean.getValue())
                .orderByDesc(CheckUnique::getId).last("limit 1")
        ;

        Integer id = checkUniqueMapper.selectList(queryWrapper).stream().findFirst().map(CheckUnique::getId).orElse(0);
        return id;
    }

    public int insertCheckUnique(CheckUniqueCreateBean checkUniqueCreateBean){
        CheckUnique entity = CheckUnique.builder()
                .type(checkUniqueCreateBean.getType())
                .value(checkUniqueCreateBean.getValue())
                .build();
        try {
            checkUniqueMapper.insert(entity);
            return entity.getId();
        }catch(DuplicateKeyException e){
            return -1;
        }
    }

    public boolean updateCheckUniqueValue(CheckUniqueUpdateBean checkUniqueUpdateBean){
        String value = checkUniqueUpdateBean.getValue();
        Integer id = checkUniqueUpdateBean.getId();
        if(value.equals("-")){
            return deleteCheckUniqueValue(id);
        }else {
            CheckUnique entity = CheckUnique.builder()
                    .id(id)
                    .value(value)
                    .build();

            return checkUniqueMapper.updateById(entity) >= 0;
        }
    }

    public boolean deleteCheckUniqueValue(Integer id){
        return checkUniqueMapper.deleteById(id) >= 0;
    }
}
