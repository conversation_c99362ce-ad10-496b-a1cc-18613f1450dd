package dao.repository.impl;

import dao.entity.AcctTransExchangeCurrency;
import dao.mapper.AcctTransExchangeCurrencyMapper;
import dao.repository.AcctTransExchangeCurrencyDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class AcctTransExchangeCurrencyDaoImpl implements AcctTransExchangeCurrencyDao {
    @Autowired
    AcctTransExchangeCurrencyMapper acctTransExchangeCurrencyMapper;

    @Override
    public boolean createAcctTransExchangeCurrency(AcctTransExchangeCurrency entity) {
        return acctTransExchangeCurrencyMapper.insert(entity) > 0;
    }

    @Override
    public AcctTransExchangeCurrency searchAcctTransExchangeCurrencyByTransId(String transId) {
        return acctTransExchangeCurrencyMapper.selectById(transId);
    }
}
