package dao.repository.impl;

import dao.entity.User;
import dao.repository.JdbcTemplateHelper;
import dao.repository.UserDao;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class UserDaoImpl extends BaseDao implements UserDao {

    private enum SP {
        getUser,
        getUser2,
        newUser
    }

    /* (non-Javadoc)
     * @see com.com.dao.impl.IUserDao#create(java.lang.String, java.lang.Integer)
     */
    @Override
    public void create(String name, Integer age) {
        getJdbcTemplate().update("insert into user(NAME, AGE) values(?, ?)", name, age);
//		getJdbcTemplateRead().update("insert into user(NAME, AGE) values(?, ?)", name, age);
    }

    /* (non-Javadoc)
     * @see com.com.dao.impl.IUserDao#getAllUsers()
     */
    @Override
    public Integer getAllUsers() {
        return getJdbcTemplate().queryForObject("select count(1) from user", Integer.class);
    }

    @Override
    public JdbcTemplateHelper getUserByName(String name) {
        Map<String, Object> param = new HashMap<>();
        param.put("in_name", name);
        return execute(SP.getUser2.name(), param);
    }

    @Override
    public JdbcTemplateHelper getUserById(int id) {
        Map<String, Object> param = new HashMap<>();
        param.put("in_id", id);
        return execute(SP.getUser.name(), param);
    }

    @Override
    public JdbcTemplateHelper createUser(String name, int age) {
        Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        param.put("age", age);
        param.put("status", 0);
        return execute(SP.newUser.name(), param);
    }

    @Override
    public JdbcTemplateHelper createUser(User user) {
        Map<String, Object> param = beanToMap(user);
        return execute(SP.newUser.name(), param);
    }
}
