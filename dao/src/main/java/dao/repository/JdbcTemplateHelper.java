package dao.repository;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class JdbcTemplateHelper {
    private Map<String, Object> resultObj;

    public JdbcTemplateHelper(Map<String, Object> resultObj) {
        this.resultObj = resultObj;
    }

    //{#result-set-1=[{name=test1, age=1}, {name=test2, age=2}, {name=test3, age=3}], #update-count-1=0}
    //{#update-count-1=1, out_name=test1, out_age=1}
    //#result-set-1
    //#update-count-1

    public int updateCount() {
        return updateCount(0);
    }

    public int updateCount(int index) {
        if (CollectionUtils.isEmpty(resultObj)) {
            return 0;
        }

        String key = "#update-count-" + (index + 1);
        Integer count = (Integer) resultObj.get(key);
        if (count == null) {
            return 0;
        }
        return count;
    }

    @SuppressWarnings("unchecked")
    public <T> T getOutput(String key) {
        if (CollectionUtils.isEmpty(this.resultObj)) {
            return null;
        }
        return (T) resultObj.get(key);
    }

    public List<Map<String, Object>> getResultSet() {
        return getResultSet(0);
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getResultSet(int index) {
        if (CollectionUtils.isEmpty(resultObj)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> resultList = (List<Map<String, Object>>) resultObj.get("#result-set-" + (index + 1));
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return resultList;
    }

    public Map<String, Object> getResultObj() {
        return resultObj;
    }

}
