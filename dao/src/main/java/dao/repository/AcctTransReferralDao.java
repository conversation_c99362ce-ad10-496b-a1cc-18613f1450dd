package dao.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import dao.bean.AcctTransReferralCreateBean;
import dao.entity.AcctTransReferral;
import dao.mapper.AcctTransReferralMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AcctTransReferralDao {
    @Autowired
    AcctTransReferralMapper acctTransReferralMapper;

    public Integer createAcctTransReferral(AcctTransReferralCreateBean bean){
        AcctTransReferral entity = AcctTransReferral.builder()
                .transId(bean.getTransId())
                .referralMemberId(bean.getReferralMemberId())
                .referralAmount(bean.getReferralAmount())
                .referralPoint(bean.getReferralPoint())
                .referralOriginalAmount(bean.getReferralOriginalAmount())
                .status(bean.getStatus())
                .modifiedBy(bean.getModifiedBy())
                .modifiedDate(bean.getModifiedDate())
                .build();
        acctTransReferralMapper.insert(entity);
        return entity.getId();
    }

    public List<AcctTransReferral> searchTransactionReferralByTransId(String transactionId){
        QueryWrapper<AcctTransReferral> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AcctTransReferral::getTransId, transactionId)
                .orderByDesc(AcctTransReferral::getId);
        return acctTransReferralMapper.selectList(queryWrapper);
    }

}
