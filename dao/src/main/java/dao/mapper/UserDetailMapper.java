package dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import dao.entity.UserDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

public interface UserDetailMapper extends BaseMapper<UserDetail> {

    @Select("select * from user_detail where id = #{id}")
    @Results(id = "userDetail")
    UserDetail getUserDetailById(@Param("id") int id);
}
