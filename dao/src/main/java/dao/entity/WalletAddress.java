package dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("walletaddress_log")
public class WalletAddress {
    @TableId(type = IdType.AUTO)
    Integer id;
    Integer orgId;
    String walletAddress;
    Date modifiedDate;
    String modifiedBy;
}
