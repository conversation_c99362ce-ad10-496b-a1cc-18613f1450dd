package dao.bean;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@Data
public class MerchantCryptocurrencyAccountDetailUpdateBean {
    private Integer accountId;
    private BigDecimal maxDailyLimitAmount;
    private Boolean webScraping;
    private BigDecimal erc20WithdrawalFee;
    private BigDecimal trc20WithdrawalFee;
    private Date modifiedDate;
    private String modifiedBy;

    public MerchantCryptocurrencyAccountDetailUpdateBean(Integer accountId) {
        this.accountId = accountId;
    }
}
