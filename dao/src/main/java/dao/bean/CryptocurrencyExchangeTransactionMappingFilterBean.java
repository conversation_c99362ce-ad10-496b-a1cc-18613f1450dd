package dao.bean;

import dao.enums.CommonStatus;
import dao.enums.TransactionMappingType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CryptocurrencyExchangeTransactionMappingFilterBean {
    private String merchantTransId;
    private CommonStatus status;
    private TransactionMappingType type;
    private Boolean isSuccess;
    private String movementId;
}
