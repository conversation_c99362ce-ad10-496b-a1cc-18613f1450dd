package dao.bean;

import dao.enums.ChargeType;
import dao.enums.CommonStatus;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentTypeLimit {
    private String paymentTypeCode;
    private String paymentGatewayCode;
    private String currencyCode;
    private BigDecimal minAmount;
    private BigDecimal maxAmount;
    private ChargeType chargeType;
    private BigDecimal chargePercent;
    private BigDecimal dailyDepositAmount;
    private CommonStatus status;
    private Integer expiredTime;
}
