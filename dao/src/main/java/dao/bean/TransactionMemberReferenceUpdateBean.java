package dao.bean;

import dao.enums.Action;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TransactionMemberReferenceUpdateBean {
    private String transactionId;
    private String memberReference;
    private Action actionId;
    private Date modifiedDate;
    private String modifiedBy;
}
