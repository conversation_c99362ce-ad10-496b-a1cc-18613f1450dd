package dao.bean;

import dao.enums.CommonStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MerchantCryptocurrencyAccountWalletAddressUpdateBean {
    private Integer id;
    private String walletAddress;
    private CommonStatus status;
    private Date modifiedDate;
    private String modifiedBy;
    private Integer retryTimes;
}
