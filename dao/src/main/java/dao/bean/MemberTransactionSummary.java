package dao.bean;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MemberTransactionSummary {
    private Integer memberId;
    private String currencyCode;
    private BigDecimal totalCount;
    private BigDecimal totalDepositAmount;
    private BigDecimal totalDepositCount;
    private BigDecimal totalWithdrawalAmount;
    private BigDecimal totalWithdrawalCount;
    private BigDecimal totalRebateAmount;
    private BigDecimal totalRebateCount;
    private BigDecimal totalSnatchAmount;
    private BigDecimal totalSnatchCount;
}
