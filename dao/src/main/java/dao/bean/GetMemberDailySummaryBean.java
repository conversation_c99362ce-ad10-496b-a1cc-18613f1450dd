package dao.bean;

import dao.enums.TransactionStatus;
import dao.enums.TransactionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetMemberDailySummaryBean {
    private Integer memberId;
    private TransactionType transactionType;
    private TransactionStatus transactionStatus;
    private Date transactionDateFrom;
    private Date transactionDateTo;
}
