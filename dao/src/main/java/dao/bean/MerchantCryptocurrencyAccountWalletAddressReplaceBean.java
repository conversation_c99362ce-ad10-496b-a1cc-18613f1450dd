package dao.bean;

import dao.enums.CommonStatus;
import dao.enums.DepositUSDTProtocol;
import lombok.Data;

import java.util.Date;

@Data
public class MerchantCryptocurrencyAccountWalletAddressReplaceBean {
    private Integer id;
    private Integer merchantCryptocurrencyAccountId;
    private DepositUSDTProtocol currency;
    private String walletAddress;
    private CommonStatus status;
    private Date modifiedDate;
    private String modifiedBy;
    private Integer retryTimes;

    public MerchantCryptocurrencyAccountWalletAddressReplaceBean(MerchantCryptocurrencyAccountWalletAddressCreateBean bean) {
        this.merchantCryptocurrencyAccountId = bean.getMerchantCryptocurrencyAccountId();
        this.walletAddress = bean.getWalletAddress();
        this.status = bean.getStatus();
        this.modifiedDate = bean.getModifiedDate();
        this.modifiedBy = bean.getModifiedBy();
        this.currency = bean.getCurrency();
        this.retryTimes = 0;
    }

    public MerchantCryptocurrencyAccountWalletAddressReplaceBean(MerchantCryptocurrencyAccountWalletAddressUpdateBean bean) {
        this.id = bean.getId();
        this.walletAddress = bean.getWalletAddress();
        this.status = bean.getStatus();
        this.modifiedDate = bean.getModifiedDate();
        this.modifiedBy = bean.getModifiedBy();
        this.retryTimes = bean.getRetryTimes();
    }
}
