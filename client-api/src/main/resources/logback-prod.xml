<included>

    <appender name="cloud-watch" class="io.github.dibog.AwsLogAppender">

        <awsConfig>
            <credentials>
                <accessKeyId>********************</accessKeyId>
                <secretAccessKey>RbKqRgnFki8NHnmq/JWQsFwxEzeKYX4LSyJYKM8C</secretAccessKey>
            </credentials>

            <profileName></profileName>

            <region>ap-northeast-1</region>

            <!--            <clientConfig class="com.amazonaws.ClientConfiguration">-->
            <!--                <proxyHost></proxyHost>-->
            <!--                <proxyPort></proxyPort>-->
            <!--            </clientConfig>-->
        </awsConfig>

        <createLogGroup>true</createLogGroup>
        <queueLength>300</queueLength>
        <groupName>PaymentAPI-PROD</groupName>
        <streamName>PaymentAPI</streamName>
        <dateFormat>yyyyMMdd</dateFormat>

        <layout>
            <pattern>%d{MM-dd HH:mm:ss.SSS}[%5p] [%t]%X{mdcData} %c{5} - %m%n</pattern>
        </layout>

    </appender>

    <root level="info">
        <appender-ref ref="cloud-watch"/>
    </root>
</included>